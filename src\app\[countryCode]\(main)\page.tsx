import { listRegions } from "@/lib/data/regions"
import FeaturedProducts from "@/modules/home/<USER>/featured-products"
import Hero from "@/modules/home/<USER>/hero"
import WhoWeAre from "@/modules/home/<USER>/who-we-are"
import HowWeHelp from "@/modules/home/<USER>/how-we-help"
import OurBrands from "@/modules/home/<USER>/our-brands"
import ProductCategories from "@/modules/home/<USER>/product-categories"
import WhyChooseUs from "@/modules/home/<USER>/why-choose-us"
import CompanyInfo from "@/modules/home/<USER>/company-info"
import GetQuote from "@/modules/home/<USER>/get-quote"
import SkeletonFeaturedProducts from "@/modules/skeletons/templates/skeleton-featured-products"
import { Metadata } from "next"
import { Suspense } from "react"

export const dynamicParams = true

export const metadata: Metadata = {
  title: "Medusa Next.js Starter Template",
  description:
    "A performant frontend ecommerce starter template with Next.js 14 and Medusa.",
}

export async function generateStaticParams() {
  const countryCodes = await listRegions().then(
    (regions) =>
      regions
        ?.map((r) => r.countries?.map((c) => c.iso_2))
        .flat()
        .filter(Boolean) as string[]
  )
  return countryCodes.map((countryCode) => ({ countryCode }))
}

export default async function Home(props: {
  params: Promise<{ countryCode: string }>
}) {
  const params = await props.params

  const { countryCode } = params

  return (
    <div className="flex flex-col">
      <Hero />
      <WhoWeAre />
      <HowWeHelp />
      <ProductCategories />
      <OurBrands />
      <WhyChooseUs />
      <CompanyInfo />
      <div className="flex flex-col">
        <Suspense fallback={<SkeletonFeaturedProducts />}>
          <FeaturedProducts countryCode={countryCode} />
        </Suspense>
      </div>
      <GetQuote />
    </div>
  )
}
