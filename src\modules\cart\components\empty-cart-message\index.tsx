import InteractiveLink from "@/modules/common/components/interactive-link"
import { Heading, Text } from "@medusajs/ui"

const EmptyCartMessage = () => {
  return (
    <div
      className="py-48 px-2 flex flex-col justify-center items-start"
      data-testid="empty-cart-message"
    >
      <Heading
        level="h1"
        className="flex flex-row text-3xl gap-x-2 items-baseline !font-jxd-bold"
        style={{ fontFamily: 'JXD-Bold, sans-serif' }}
      >
        Cart
      </Heading>
      <Text className="text-base mt-4 mb-6 max-w-[32rem] !font-jxd-regular" style={{ fontFamily: 'JXD-Regular, sans-serif' }}>
        You don&apos;t have anything in your cart. Let&apos;s change that, use
        the link below to start browsing our products.
      </Text>
      <div>
        <InteractiveLink href="/store">Explore products</InteractiveLink>
      </div>
    </div>
  )
}

export default EmptyCartMessage
