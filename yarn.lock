# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10

"@alloc/quick-lru@npm:^5.2.0":
  version: 5.2.0
  resolution: "@alloc/quick-lru@npm:5.2.0"
  checksum: 10/bdc35758b552bcf045733ac047fb7f9a07c4678b944c641adfbd41f798b4b91fffd0fdc0df2578d9b0afc7b4d636aa6e110ead5d6281a2adc1ab90efd7f057f8
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10/f3451525379c68a73eb0a1e65247fbf28c0cccd126d93af21c75fceff77773d43c0d4a2d51978fb131aff25b5f2cb41a9fe48cc296e61ae65e679c4f6918b0ab
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 10/721b8a6e360a1fa0f1c9fe7351ae6c874828e119183688b533c477aa378f1010f37cc9afbfc4722c686d1f5cdd00da02eab4ba7278a0c504fa0d7a321dcd4fdf
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/compat-data@npm:7.27.2"
  checksum: 10/eaa9f8aaeb9475779f4411fa397f712a6441b650d4e0b40c5535c954c891cd35c0363004db42902192aa8224532ac31ce06890478b060995286fe4fadd54e542
  languageName: node
  linkType: hard

"@babel/core@npm:^7.17.5":
  version: 7.27.1
  resolution: "@babel/core@npm:7.27.1"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.1"
    "@babel/helper-compilation-targets": "npm:^7.27.1"
    "@babel/helper-module-transforms": "npm:^7.27.1"
    "@babel/helpers": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10/3dfec88f84b3ce567e6c482db0119f02f451bd3f86b0835c71c029fedb657969786507fafedd3a0732bd1be9fbc9f0635d734efafabad6dbc67d3eb7b494cdd8
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/generator@npm:7.27.1"
  dependencies:
    "@babel/parser": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10/6101825922a8a116e64b507d9309b38c5bc027b333d7111fcb760422741d3c72bd8f8e5aa935c2944c434ffe376353a27afa3a25a8526dc2ef90743d266770db
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.27.1":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-validator-option": "npm:^7.27.1"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10/bd53c30a7477049db04b655d11f4c3500aea3bcbc2497cf02161de2ecf994fec7c098aabbcebe210ffabc2ecbdb1e3ffad23fb4d3f18723b814f423ea1749fe8
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/58e792ea5d4ae71676e0d03d9fef33e886a09602addc3bd01388a98d87df9fcfd192968feb40ac4aedb7e287ec3d0c17b33e3ecefe002592041a91d8a1998a8d
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-transforms@npm:7.27.1"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10/415509a5854203073755aab3ad293664146a55777355b5b5187902f976162c9565907d2276f7f6e778527be4829db2d926015d446100a65f2538d6397d83e248
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 10/0ae29cc2005084abdae2966afdb86ed14d41c9c37db02c3693d5022fba9f5d59b011d039380b8e537c34daf117c549f52b452398f576e908fb9db3c7abbb3a00
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 10/75041904d21bdc0cd3b07a8ac90b11d64cd3c881e89cb936fa80edd734bf23c35e6bd1312611e8574c4eab1f3af0f63e8a5894f4699e9cfdf70c06fcf4252320
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: 10/db73e6a308092531c629ee5de7f0d04390835b21a263be2644276cb27da2384b64676cab9f22cd8d8dbd854c92b1d7d56fc8517cf0070c35d1c14a8c828b0903
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helpers@npm:7.27.1"
  dependencies:
    "@babel/template": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/b86ee2c87d52640c63ec1fdf139d4560efc173ae6379659e0df49a3c0cf1d5f24436132ebb4459a4ee72418b43b39ee001f4e01465b48c8d31911a745ec4fd74
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.27.1, @babel/parser@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/parser@npm:7.27.2"
  dependencies:
    "@babel/types": "npm:^7.27.1"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10/133b4ccfbc01d4f36b0945937aabff87026c29fda6dcd3c842053a672e50f2487a101a3acd150bbaa2eecd33f3bd35650f95b806567c926f93b2af35c2b615c9
  languageName: node
  linkType: hard

"@babel/template@npm:^7.27.1":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: 10/fed15a84beb0b9340e5f81566600dbee5eccd92e4b9cc42a944359b1aa1082373391d9d5fc3656981dff27233ec935d0bc96453cf507f60a4b079463999244d8
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/traverse@npm:7.27.1"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.1"
    "@babel/template": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10/9977271aa451293d3f184521412788d6ddaff9d6a29626d7435b5dacd059feb2d7753bc94f59f4f5b76e65bd2e2cabc8a10d7e1f93709feda28619f2e8cbf4d6
  languageName: node
  linkType: hard

"@babel/types@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/types@npm:7.27.1"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10/81f8ada28c4b29695d7d4c4cbfaa5ec3138ccebbeb26628c7c3cc570fdc84f28967c9e68caf4977d51ff4f4d3159c88857ef278317f84f3515dd65e5b8a74995
  languageName: node
  linkType: hard

"@emnapi/core@npm:^1.4.0":
  version: 1.4.3
  resolution: "@emnapi/core@npm:1.4.3"
  dependencies:
    "@emnapi/wasi-threads": "npm:1.0.2"
    tslib: "npm:^2.4.0"
  checksum: 10/b511f66b897d2019835391544fdf11f4fa0ce06cc1181abfa17c7d4cf03aaaa4fc8a64fcd30bb3f901de488d0a6f370b53a8de2215a898f5a4ac98015265b3b7
  languageName: node
  linkType: hard

"@emnapi/runtime@npm:^1.4.0":
  version: 1.4.3
  resolution: "@emnapi/runtime@npm:1.4.3"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10/4f90852a1a5912982cc4e176b6420556971bcf6a85ee23e379e2455066d616219751367dcf43e6a6eaf41ea7e95ba9dc830665a52b5d979dfe074237d19578f8
  languageName: node
  linkType: hard

"@emnapi/wasi-threads@npm:1.0.2":
  version: 1.0.2
  resolution: "@emnapi/wasi-threads@npm:1.0.2"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10/e82941776665eb958c2084728191d6b15a94383449975c4621b67a1c8217e1c0ec11056a693906c76863cb96f782f8be500510ecec6874e3f5da35a8e7968cfd
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.7.0":
  version: 4.7.0
  resolution: "@eslint-community/eslint-utils@npm:4.7.0"
  dependencies:
    eslint-visitor-keys: "npm:^3.4.3"
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: 10/43ed5d391526d9f5bbe452aef336389a473026fca92057cf97c576db11401ce9bcf8ef0bf72625bbaf6207ed8ba6bf0dcf4d7e809c24f08faa68a28533c491a7
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 10/c08f1dd7dd18fbb60bdd0d85820656d1374dd898af9be7f82cb00451313402a22d5e30569c150315b4385907cdbca78c22389b2a72ab78883b3173be317620cc
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^1.2.0":
  version: 1.4.1
  resolution: "@eslint/eslintrc@npm:1.4.1"
  dependencies:
    ajv: "npm:^6.12.4"
    debug: "npm:^4.3.2"
    espree: "npm:^9.4.0"
    globals: "npm:^13.19.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.2.1"
    js-yaml: "npm:^4.1.0"
    minimatch: "npm:^3.1.2"
    strip-json-comments: "npm:^3.1.1"
  checksum: 10/e587923ba913d90865eae73308e77750da8b85de941f8df990e0efeec3cc188d2f66c0c5085fdb50748b2e43d11bf7c0cae9f2e743369b3d3a36e14a7704a2b1
  languageName: node
  linkType: hard

"@floating-ui/core@npm:^1.7.0":
  version: 1.7.0
  resolution: "@floating-ui/core@npm:1.7.0"
  dependencies:
    "@floating-ui/utils": "npm:^0.2.9"
  checksum: 10/b047b9b5e18d9c2b737aa1cd0aadd138da15e6ca4ba35cc8b41eff280a66f84c749739234d782e8f250de0d6d5afed4b1d06ed9bf2635e0743aa9868d32fe94a
  languageName: node
  linkType: hard

"@floating-ui/dom@npm:^1.0.0":
  version: 1.7.0
  resolution: "@floating-ui/dom@npm:1.7.0"
  dependencies:
    "@floating-ui/core": "npm:^1.7.0"
    "@floating-ui/utils": "npm:^0.2.9"
  checksum: 10/9c3561981ea389fe39b7095d7a3771fd906580eedc43fda0eb554faa5f2f9756169fef1824d66198314a41c7f8d63e56bc7f5b24fc528471c9e6bc43cafa6a00
  languageName: node
  linkType: hard

"@floating-ui/react-dom@npm:^2.0.0, @floating-ui/react-dom@npm:^2.1.2":
  version: 2.1.2
  resolution: "@floating-ui/react-dom@npm:2.1.2"
  dependencies:
    "@floating-ui/dom": "npm:^1.0.0"
  peerDependencies:
    react: ">=16.8.0"
    react-dom: ">=16.8.0"
  checksum: 10/2a67dc8499674e42ff32c7246bded185bb0fdd492150067caf9568569557ac4756a67787421d8604b0f241e5337de10762aee270d9aeef106d078a0ff13596c4
  languageName: node
  linkType: hard

"@floating-ui/react@npm:^0.26.16":
  version: 0.26.28
  resolution: "@floating-ui/react@npm:0.26.28"
  dependencies:
    "@floating-ui/react-dom": "npm:^2.1.2"
    "@floating-ui/utils": "npm:^0.2.8"
    tabbable: "npm:^6.0.0"
  peerDependencies:
    react: ">=16.8.0"
    react-dom: ">=16.8.0"
  checksum: 10/7f8e6b27db48b68ca94756687af21857be04e7360ac922d7c8e22411f2895df6384af7bd40f4b48663d3cc5809bb5c6574cd9c9ea15543ec747b9a8e1c8c3008
  languageName: node
  linkType: hard

"@floating-ui/utils@npm:^0.2.8, @floating-ui/utils@npm:^0.2.9":
  version: 0.2.9
  resolution: "@floating-ui/utils@npm:0.2.9"
  checksum: 10/0ca786347db3dd8d9034b86d1449fabb96642788e5900cc5f2aee433cd7b243efbcd7a165bead50b004ee3f20a90ddebb6a35296fc41d43cfd361b6f01b69ffb
  languageName: node
  linkType: hard

"@formatjs/ecma402-abstract@npm:2.3.4":
  version: 2.3.4
  resolution: "@formatjs/ecma402-abstract@npm:2.3.4"
  dependencies:
    "@formatjs/fast-memoize": "npm:2.2.7"
    "@formatjs/intl-localematcher": "npm:0.6.1"
    decimal.js: "npm:^10.4.3"
    tslib: "npm:^2.8.0"
  checksum: 10/573971ffc291096a4b9fcc80b4708124e89bf2e3ac50e0f78b41eb797e9aa1b842f4dc3665e4467a853c738386821769d9e40408a1d25bc73323a1f057a16cf2
  languageName: node
  linkType: hard

"@formatjs/fast-memoize@npm:2.2.7":
  version: 2.2.7
  resolution: "@formatjs/fast-memoize@npm:2.2.7"
  dependencies:
    tslib: "npm:^2.8.0"
  checksum: 10/e7e6efc677d63a13d99a854305db471b69f64cbfebdcb6dbe507dab9aa7eaae482ca5de86f343c856ca0a2c8f251672bd1f37c572ce14af602c0287378097d43
  languageName: node
  linkType: hard

"@formatjs/icu-messageformat-parser@npm:2.11.2":
  version: 2.11.2
  resolution: "@formatjs/icu-messageformat-parser@npm:2.11.2"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:2.3.4"
    "@formatjs/icu-skeleton-parser": "npm:1.8.14"
    tslib: "npm:^2.8.0"
  checksum: 10/e919eb2a132ac1d54fb1a7e3a3254007649b55196d3818090df92a4268dcddf20cbdf863c06039fbbe7a35a8a3f17bdc172dade99d1f17c1d8a95dcec444c3e3
  languageName: node
  linkType: hard

"@formatjs/icu-skeleton-parser@npm:1.8.14":
  version: 1.8.14
  resolution: "@formatjs/icu-skeleton-parser@npm:1.8.14"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:2.3.4"
    tslib: "npm:^2.8.0"
  checksum: 10/2fbe3155c310358820b118d8c9844f314eff3500a82f1c65402434a3095823e1afeaab8d1762b4a59cc5679d82dc4c8c134683565d7cdae4daace23251f46a47
  languageName: node
  linkType: hard

"@formatjs/intl-localematcher@npm:0.6.1":
  version: 0.6.1
  resolution: "@formatjs/intl-localematcher@npm:0.6.1"
  dependencies:
    tslib: "npm:^2.8.0"
  checksum: 10/c7b3bc8395d18670677f207b2fd107561fff5d6394a9b4273c29e0bea920300ec3a2eefead600ebb7761c04a770cada28f78ac059f84d00520bfb57a9db36998
  languageName: node
  linkType: hard

"@headlessui/react@npm:^2.2.0":
  version: 2.2.2
  resolution: "@headlessui/react@npm:2.2.2"
  dependencies:
    "@floating-ui/react": "npm:^0.26.16"
    "@react-aria/focus": "npm:^3.17.1"
    "@react-aria/interactions": "npm:^3.21.3"
    "@tanstack/react-virtual": "npm:^3.13.6"
    use-sync-external-store: "npm:^1.5.0"
  peerDependencies:
    react: ^18 || ^19 || ^19.0.0-rc
    react-dom: ^18 || ^19 || ^19.0.0-rc
  checksum: 10/59a0686816f332b366a2e314408bccf71ff528580ee7223052316326cebe0536dc0b63c9cf50772a04147b240fa35c2434fdc84a386e45dc9339ca2485e1f22d
  languageName: node
  linkType: hard

"@hookform/resolvers@npm:^3.9.0":
  version: 3.10.0
  resolution: "@hookform/resolvers@npm:3.10.0"
  peerDependencies:
    react-hook-form: ^7.0.0
  checksum: 10/92d3601b2cedb4f52e9e086cc4bd6a20b493f59ecfb1af8b8e465057f0445aa83cf5a6d482a831cdf5d1321a73269173633222eefde0adb515d3820c1525b0cb
  languageName: node
  linkType: hard

"@humanwhocodes/config-array@npm:^0.9.2":
  version: 0.9.5
  resolution: "@humanwhocodes/config-array@npm:0.9.5"
  dependencies:
    "@humanwhocodes/object-schema": "npm:^1.2.1"
    debug: "npm:^4.1.1"
    minimatch: "npm:^3.0.4"
  checksum: 10/98abf18a2e7b6affa278f5feecf8bc7ddaa40bbe3d8f20294208fe0be840cb697c538f6cb9035b722431634cdaade4680b1102e7d5c7573ca93a43aacea02a4b
  languageName: node
  linkType: hard

"@humanwhocodes/object-schema@npm:^1.2.1":
  version: 1.2.1
  resolution: "@humanwhocodes/object-schema@npm:1.2.1"
  checksum: 10/b48a8f87fcd5fdc4ac60a31a8bf710d19cc64556050575e6a35a4a48a8543cf8cde1598a65640ff2cdfbfd165b38f9db4fa3782bea7848eb585cc3db824002e6
  languageName: node
  linkType: hard

"@img/sharp-darwin-arm64@npm:0.34.1":
  version: 0.34.1
  resolution: "@img/sharp-darwin-arm64@npm:0.34.1"
  dependencies:
    "@img/sharp-libvips-darwin-arm64": "npm:1.1.0"
  dependenciesMeta:
    "@img/sharp-libvips-darwin-arm64":
      optional: true
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-darwin-x64@npm:0.34.1":
  version: 0.34.1
  resolution: "@img/sharp-darwin-x64@npm:0.34.1"
  dependencies:
    "@img/sharp-libvips-darwin-x64": "npm:1.1.0"
  dependenciesMeta:
    "@img/sharp-libvips-darwin-x64":
      optional: true
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-arm64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-darwin-arm64@npm:1.1.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-x64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-darwin-x64@npm:1.1.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-arm64@npm:1.1.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-arm@npm:1.1.0"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-ppc64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-ppc64@npm:1.1.0"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-s390x@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-s390x@npm:1.1.0"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-x64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-x64@npm:1.1.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-arm64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linuxmusl-arm64@npm:1.1.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-x64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linuxmusl-x64@npm:1.1.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-linux-arm64@npm:0.34.1":
  version: 0.34.1
  resolution: "@img/sharp-linux-arm64@npm:0.34.1"
  dependencies:
    "@img/sharp-libvips-linux-arm64": "npm:1.1.0"
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm64":
      optional: true
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-arm@npm:0.34.1":
  version: 0.34.1
  resolution: "@img/sharp-linux-arm@npm:0.34.1"
  dependencies:
    "@img/sharp-libvips-linux-arm": "npm:1.1.0"
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm":
      optional: true
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-s390x@npm:0.34.1":
  version: 0.34.1
  resolution: "@img/sharp-linux-s390x@npm:0.34.1"
  dependencies:
    "@img/sharp-libvips-linux-s390x": "npm:1.1.0"
  dependenciesMeta:
    "@img/sharp-libvips-linux-s390x":
      optional: true
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-x64@npm:0.34.1":
  version: 0.34.1
  resolution: "@img/sharp-linux-x64@npm:0.34.1"
  dependencies:
    "@img/sharp-libvips-linux-x64": "npm:1.1.0"
  dependenciesMeta:
    "@img/sharp-libvips-linux-x64":
      optional: true
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-arm64@npm:0.34.1":
  version: 0.34.1
  resolution: "@img/sharp-linuxmusl-arm64@npm:0.34.1"
  dependencies:
    "@img/sharp-libvips-linuxmusl-arm64": "npm:1.1.0"
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-x64@npm:0.34.1":
  version: 0.34.1
  resolution: "@img/sharp-linuxmusl-x64@npm:0.34.1"
  dependencies:
    "@img/sharp-libvips-linuxmusl-x64": "npm:1.1.0"
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-wasm32@npm:0.34.1":
  version: 0.34.1
  resolution: "@img/sharp-wasm32@npm:0.34.1"
  dependencies:
    "@emnapi/runtime": "npm:^1.4.0"
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@img/sharp-win32-ia32@npm:0.34.1":
  version: 0.34.1
  resolution: "@img/sharp-win32-ia32@npm:0.34.1"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@img/sharp-win32-x64@npm:0.34.1":
  version: 0.34.1
  resolution: "@img/sharp-win32-x64@npm:0.34.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@internationalized/date@npm:^3.8.0":
  version: 3.8.0
  resolution: "@internationalized/date@npm:3.8.0"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  checksum: 10/01e783001f788e8a8c97ea86f577cd3f2c92cbdf86445f8824c60ca2f7c4b02798b2ed55bd193c9ef72af15af5cdb196eb51ab0509ee1e7240d015cb5cb10550
  languageName: node
  linkType: hard

"@internationalized/message@npm:^3.1.7":
  version: 3.1.7
  resolution: "@internationalized/message@npm:3.1.7"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
    intl-messageformat: "npm:^10.1.0"
  checksum: 10/57d0a7277e7c90b478f4981707f3222f3de2603aad1ac6bf42deb8bbc6eb5695496906e2e852385cfc3fe860d928e994bd01fe1dde144e415a41231c52348f27
  languageName: node
  linkType: hard

"@internationalized/number@npm:^3.6.1":
  version: 3.6.1
  resolution: "@internationalized/number@npm:3.6.1"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  checksum: 10/52de824178bf9af0c674603cbb9d4347cbfb3033ad3586997530c9e03f62caa04028144e0632d52580047639e5b786b97faa4c8164e3a50b078a7d0da06f244a
  languageName: node
  linkType: hard

"@internationalized/string@npm:^3.2.6":
  version: 3.2.6
  resolution: "@internationalized/string@npm:3.2.6"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  checksum: 10/d9fa35814be43e9b6770f3f9280581d7bbd8022861ab2223c9366cadee186c9be236a9a48af1dadf0aad1591ffc11cf104dd587a367a65c453bbc487a6bcce3d
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10/e9ed5fd27c3aec1095e3a16e0c0cf148d1fee55a38665c35f7b3f86a9b5d00d042ddaabc98e8a1cb7463b9378c15f22a94eb35e99469c201453eb8375191f243
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10/4412e9e6713c89c1e66d80bb0bb5a2a93192f10477623a27d08f228ba0316bb880affabc5bfe7f838f58a34d26c2c190da726e576cdfc18c49a72e89adabdcf5
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.2, @jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10/9d3a56ab3612ab9b85d38b2a93b87f3324f11c5130859957f6500e4ac8ce35f299d5ccc3ecd1ae87597601ecf83cee29e9afd04c18777c24011073992ff946df
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10/97106439d750a409c22c8bff822d648f6a71f3aa9bc8e5129efdc36343cd3096ddc4eeb1c62d2fe48e9bdd4db37b05d4646a17114ecebd3bbcacfa2de51c3c1d
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10/832e513a85a588f8ed4f27d1279420d8547743cc37fcad5a5a76fc74bb895b013dfe614d0eed9cb860048e6546b798f8f2652020b4b2ba0561b05caa8c654b10
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.6
  resolution: "@jridgewell/source-map@npm:0.3.6"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
  checksum: 10/0a9aca9320dc9044014ba0ef989b3a8411b0d778895553e3b7ca2ac0a75a20af4a5ad3f202acfb1879fa40466036a4417e1d5b38305baed8b9c1ebe6e4b3e7f5
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10/4ed6123217569a1484419ac53f6ea0d9f3b57e5b57ab30d7c267bdb27792a27eb0e4b08e84a2680aa55cc2f2b411ffd6ec3db01c44fdc6dc43aca4b55f8374fd
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10/dced32160a44b49d531b80a4a2159dceab6b3ddf0c8e95a0deae4b0e894b172defa63d5ac52a19c2068e1fe7d31ea4ba931fbeec103233ecb4208953967120fc
  languageName: node
  linkType: hard

"@medusajs/icons@npm:2.8.4, @medusajs/icons@npm:latest":
  version: 2.8.4
  resolution: "@medusajs/icons@npm:2.8.4"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  checksum: 10/ce6d7b1d0bd0b769dd97209933b8a9b397a3f0cdf869d036b6f61d2c1901e8ef515fcda0c1a57666f95cb0aa87a6fc38cdf3c23910f15711352b24e52f68bcf9
  languageName: node
  linkType: hard

"@medusajs/js-sdk@npm:latest":
  version: 2.8.4
  resolution: "@medusajs/js-sdk@npm:2.8.4"
  dependencies:
    "@medusajs/types": "npm:2.8.4"
    fetch-event-stream: "npm:^0.1.5"
    qs: "npm:^6.12.1"
  checksum: 10/4c3a6512d1f3df16ca2a2cde30193d9df5e26a82faaeac18f817f2f76d943d0ee84f1a19478caaa0570436b7826f8bc298bd89accb11ade33a9968ba39db3a99
  languageName: node
  linkType: hard

"@medusajs/types@npm:2.8.4, @medusajs/types@npm:latest":
  version: 2.8.4
  resolution: "@medusajs/types@npm:2.8.4"
  dependencies:
    bignumber.js: "npm:^9.1.2"
  peerDependencies:
    awilix: ^8.0.1
    ioredis: ^5.4.1
    vite: ^5 || ^6
  peerDependenciesMeta:
    ioredis:
      optional: true
    vite:
      optional: true
  checksum: 10/0fac4c89e43e9c77cf81541be9328d2dead9f06a2d6ccf425c8a6a61a902f374ea6bacd272a8ac624846ee38ee2435a2b7e57a00c46ccbf4ba45df68aacd13d0
  languageName: node
  linkType: hard

"@medusajs/ui-preset@npm:latest":
  version: 2.8.4
  resolution: "@medusajs/ui-preset@npm:2.8.4"
  dependencies:
    "@tailwindcss/forms": "npm:^0.5.3"
    tailwindcss-animate: "npm:^1.0.6"
  peerDependencies:
    tailwindcss: ">=3.0.0"
  checksum: 10/e8c5f23f77c3928a46581cc2de68846899f2e29eef558d59fa7ce8f7b56e44032cf422265a213054723281c025fff33d3eb4baeb3c4c3070c5f385fd8347a2b4
  languageName: node
  linkType: hard

"@medusajs/ui@npm:latest":
  version: 4.0.14
  resolution: "@medusajs/ui@npm:4.0.14"
  dependencies:
    "@medusajs/icons": "npm:2.8.4"
    "@tanstack/react-table": "npm:8.20.5"
    clsx: "npm:^1.2.1"
    copy-to-clipboard: "npm:^3.3.3"
    cva: "npm:1.0.0-beta.1"
    prism-react-renderer: "npm:^2.0.6"
    prismjs: "npm:^1.29.0"
    radix-ui: "npm:1.1.2"
    react-aria: "npm:^3.33.1"
    react-currency-input-field: "npm:^3.6.11"
    react-stately: "npm:^3.31.1"
    sonner: "npm:^1.5.0"
    tailwind-merge: "npm:^2.2.1"
  peerDependencies:
    react: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  checksum: 10/4e4f3a23373c51fd1ddd2a6c6e113ec04a23c8484d2c918498e538b54b8379487006605f29ad39837c365039127ed8054aedff117aca6d0b19cf595bdf2c4cc9
  languageName: node
  linkType: hard

"@napi-rs/wasm-runtime@npm:^0.2.9":
  version: 0.2.9
  resolution: "@napi-rs/wasm-runtime@npm:0.2.9"
  dependencies:
    "@emnapi/core": "npm:^1.4.0"
    "@emnapi/runtime": "npm:^1.4.0"
    "@tybys/wasm-util": "npm:^0.9.0"
  checksum: 10/8ebc7d85e11e1b8d71908d5615ff24b27ef7af8287d087fb5cff5a3e545915c7545998d976a9cd6a4315dab4ba0f609439fbe6408fec3afebd288efb0dbdc135
  languageName: node
  linkType: hard

"@next/env@npm:15.3.2":
  version: 15.3.2
  resolution: "@next/env@npm:15.3.2"
  checksum: 10/433eabaa23affbcb1c55f8a67161de911ebfec2fa917071ad4fce34b5e8d60632d769e01fd966dbbc1bec6cedce8958bb90996a7cc0d6804ffc51663dde7d43e
  languageName: node
  linkType: hard

"@next/eslint-plugin-next@npm:15.0.1":
  version: 15.0.1
  resolution: "@next/eslint-plugin-next@npm:15.0.1"
  dependencies:
    fast-glob: "npm:3.3.1"
  checksum: 10/1216df35426e8e723f1d1633f724234c334f89bb6dec3b8a38faa16168c4bcf7a7b991d2937c0a6b4b5f90d77dd3e71f9e8b96c2c17366d6a152dc0be42c2384
  languageName: node
  linkType: hard

"@next/swc-darwin-arm64@npm:15.3.2":
  version: 15.3.2
  resolution: "@next/swc-darwin-arm64@npm:15.3.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-darwin-x64@npm:15.3.2":
  version: 15.3.2
  resolution: "@next/swc-darwin-x64@npm:15.3.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-gnu@npm:15.3.2":
  version: 15.3.2
  resolution: "@next/swc-linux-arm64-gnu@npm:15.3.2"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-musl@npm:15.3.2":
  version: 15.3.2
  resolution: "@next/swc-linux-arm64-musl@npm:15.3.2"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-linux-x64-gnu@npm:15.3.2":
  version: 15.3.2
  resolution: "@next/swc-linux-x64-gnu@npm:15.3.2"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-x64-musl@npm:15.3.2":
  version: 15.3.2
  resolution: "@next/swc-linux-x64-musl@npm:15.3.2"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-win32-arm64-msvc@npm:15.3.2":
  version: 15.3.2
  resolution: "@next/swc-win32-arm64-msvc@npm:15.3.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-win32-x64-msvc@npm:15.3.2":
  version: 15.3.2
  resolution: "@next/swc-win32-x64-msvc@npm:15.3.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10/6ab2a9b8a1d67b067922c36f259e3b3dfd6b97b219c540877a4944549a4d49ea5ceba5663905ab5289682f1f3c15ff441d02f0447f620a42e1cb5e1937174d4b
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10/012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10/40033e33e96e97d77fba5a238e4bba4487b8284678906a9f616b5579ddaf868a18874c0054a75402c9fbaaa033a25ceae093af58c9c30278e35c23c9479e79b0
  languageName: node
  linkType: hard

"@nolyfill/is-core-module@npm:1.0.39":
  version: 1.0.39
  resolution: "@nolyfill/is-core-module@npm:1.0.39"
  checksum: 10/0d6e098b871eca71d875651288e1f0fa770a63478b0b50479c99dc760c64175a56b5b04f58d5581bbcc6b552b8191ab415eada093d8df9597ab3423c8cac1815
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10/775c9a7eb1f88c195dfb3bce70c31d0fe2a12b28b754e25c08a3edb4bc4816bfedb7ac64ef1e730579d078ca19dacf11630e99f8f3c3e0fd7b23caa5fd6d30a6
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10/405c4490e1ff11cf299775449a3c254a366a4b1ffc79d87159b0ee7d5558ac9f6a2f8c0735fd6ff3873cef014cb1a44a5f9127cb6a1b2dbc408718cca9365b5a
  languageName: node
  linkType: hard

"@paypal/paypal-js@npm:^5.0.6, @paypal/paypal-js@npm:^5.1.6":
  version: 5.1.6
  resolution: "@paypal/paypal-js@npm:5.1.6"
  dependencies:
    promise-polyfill: "npm:^8.3.0"
  checksum: 10/24d630e06be220dc75e421a15253f241183da5bd6cc4884de5f257a2f0d370c4616292dc4fe9673a284fa708472cab4877c1399dcff920a8985741624da59cca
  languageName: node
  linkType: hard

"@paypal/react-paypal-js@npm:^7.8.1":
  version: 7.8.3
  resolution: "@paypal/react-paypal-js@npm:7.8.3"
  dependencies:
    "@paypal/paypal-js": "npm:^5.1.6"
    "@paypal/sdk-constants": "npm:^1.0.122"
  peerDependencies:
    react: ">=16.3.0"
    react-dom: ">=16.3.0"
  checksum: 10/6d5ada92c48ead65265848cf0dabf47951069e634f52d7c912d876f40ab77ffc95fcc03ece17a9191e5c7d453c6cfc15605a21d57352a15fac5d82024d3164b1
  languageName: node
  linkType: hard

"@paypal/sdk-constants@npm:^1.0.122":
  version: 1.0.153
  resolution: "@paypal/sdk-constants@npm:1.0.153"
  dependencies:
    hi-base32: "npm:^0.5.0"
  checksum: 10/527a10b83e5b268cab720b3bb5b042dc3cfff9532d6e8ab04e5e93dd7497717c8e71095cb9887645dde76d9dc1a8f9478be2ca1f2886b7e27a46775159e634ae
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10/115e8ceeec6bc69dff2048b35c0ab4f8bbee12d8bb6c1f4af758604586d802b6e669dcb02dda61d078de42c2b4ddce41b3d9e726d7daa6b4b850f4adbf7333ff
  languageName: node
  linkType: hard

"@radix-ui/number@npm:1.1.0":
  version: 1.1.0
  resolution: "@radix-ui/number@npm:1.1.0"
  checksum: 10/e4fc7483c19141c25dbaf3d140b75e2b7fed0bfa3ad969f4441f0266ed34b35413f57a35df7b025e2a977152bbe6131849d3444fc6f15a73345dfc2bfdc105fa
  languageName: node
  linkType: hard

"@radix-ui/primitive@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/primitive@npm:1.1.1"
  checksum: 10/d7e819177590108b74139809d52ec043c0962ae3513e947998be575fb13639c5c1c091896ddcf1d6a22a777d44ade59d22c2019ce9099607fc62a5de09c59707
  languageName: node
  linkType: hard

"@radix-ui/primitive@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/primitive@npm:1.1.2"
  checksum: 10/6cb2ac097faf77b7288bdfd87d92e983e357252d00ee0d2b51ad8e7897bf9f51ec53eafd7dd64c613671a2b02cb8166177bc3de444a6560ec60835c363321c18
  languageName: node
  linkType: hard

"@radix-ui/react-accessible-icon@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-accessible-icon@npm:1.1.1"
  dependencies:
    "@radix-ui/react-visually-hidden": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/13bb2a0cfabbef99d490264b2785dd2081ea93e95a99d4f55c5ab8aa978441c00ffe17de006a88331648502e0c58548615a441d93978bc730222a441892a7b46
  languageName: node
  linkType: hard

"@radix-ui/react-accordion@npm:1.2.2":
  version: 1.2.2
  resolution: "@radix-ui/react-accordion@npm:1.2.2"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-collapsible": "npm:1.1.2"
    "@radix-ui/react-collection": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-direction": "npm:1.1.0"
    "@radix-ui/react-id": "npm:1.1.0"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/df74f513e9bb5a147335a47ac65538f91302df96829ab75c75ad2ba5109088fca0122639a5bdb41f0ed0583eb70a51f302af45e345c4de23776925a4832cb55b
  languageName: node
  linkType: hard

"@radix-ui/react-alert-dialog@npm:1.1.5":
  version: 1.1.5
  resolution: "@radix-ui/react-alert-dialog@npm:1.1.5"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-dialog": "npm:1.1.5"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-slot": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/9bfc4c967788a12669d15917f60a680be44d7725daba2c226988c8f17bdceff5c45e9bbaaaba2f0d482b73bb0704484c326faf157fb7d376cde73f663941ec53
  languageName: node
  linkType: hard

"@radix-ui/react-arrow@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-arrow@npm:1.1.1"
  dependencies:
    "@radix-ui/react-primitive": "npm:2.0.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/c75505c2858cffff7c742e888b635879f9a6d95e08bf5ae939be33f97e1171379bc6b5354ec0cd3d12624bdbe5a830ee6aa0fb1f46b1af160b488bc54e64d486
  languageName: node
  linkType: hard

"@radix-ui/react-aspect-ratio@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-aspect-ratio@npm:1.1.1"
  dependencies:
    "@radix-ui/react-primitive": "npm:2.0.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/e5f2c8f25050b3b1accad48dc8673eb53140a50d52d6a090addf6bbb7d297c6ad4139509b781925ff5ca1b53860d36f1ae8a306e1a07f2029619dbcde752e8d1
  languageName: node
  linkType: hard

"@radix-ui/react-avatar@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-avatar@npm:1.1.2"
  dependencies:
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-callback-ref": "npm:1.1.0"
    "@radix-ui/react-use-layout-effect": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/0120c47a062c9922d7700234a4d2d851bd9e4a42951ca035a2d7b99abec152312bce41b811f36a2f5dfa7ae795e8dc77bad676c1a2f09360f754c17e65b309e4
  languageName: node
  linkType: hard

"@radix-ui/react-checkbox@npm:1.1.3":
  version: 1.1.3
  resolution: "@radix-ui/react-checkbox@npm:1.1.3"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-presence": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
    "@radix-ui/react-use-previous": "npm:1.1.0"
    "@radix-ui/react-use-size": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/e0408961c67dc22c938676ee1648a9a6e0fc9d9282c8a1fe532fe0df2d22e95ef7ef2f4f4a21e30942d9aa2d4b784093409ac6e3e0419f123b256c9b919ac917
  languageName: node
  linkType: hard

"@radix-ui/react-collapsible@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-collapsible@npm:1.1.2"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-id": "npm:1.1.0"
    "@radix-ui/react-presence": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
    "@radix-ui/react-use-layout-effect": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/623f6d45816548e2ac764839cfac4c2d0189cf8d87079283e2734a2d67503395963239b07f655f835b8e438866fea1791da1515ed31c496ed1ccd2ae60c690aa
  languageName: node
  linkType: hard

"@radix-ui/react-collection@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-collection@npm:1.1.1"
  dependencies:
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-slot": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/88b53075f345ba0354e4ec6f4f35a4160379020b48a709f12c1963052dfa8475329b842a652d79ac54238f2884c85c2c793331d84713715d2452d535d14df36a
  languageName: node
  linkType: hard

"@radix-ui/react-compose-refs@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-compose-refs@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/1be82f9f7fab96cc10f167a2e4f976e0135a63d473334f664c06f02af13bc5ea1994cb0505f89ed190d756cb65d57506721c030908af07e49b9e3cfd36044f33
  languageName: node
  linkType: hard

"@radix-ui/react-compose-refs@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-compose-refs@npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/9a91f0213014ffa40c5b8aae4debb993be5654217e504e35aa7422887eb2d114486d37e53c482d0fffb00cd44f51b5269fcdf397b280c71666fa11b7f32f165d
  languageName: node
  linkType: hard

"@radix-ui/react-context-menu@npm:2.2.5":
  version: 2.2.5
  resolution: "@radix-ui/react-context-menu@npm:2.2.5"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-menu": "npm:2.1.5"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-callback-ref": "npm:1.1.0"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/6a4079e826e2438c86074497302bbe5542b7bad80bc29c61ace510ee11b14155eb772bdc11092ab44c6d7c8e23fdd7c896a8736e840e12604cee1ccc5104cc13
  languageName: node
  linkType: hard

"@radix-ui/react-context@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-context@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/f6469583bf11cc7bff3ea5c95c56b0774a959512adead00dc64b0527cca01b90b476ca39a64edfd7e18e428e17940aa0339116b1ce5b6e8eab513cfd1065d391
  languageName: node
  linkType: hard

"@radix-ui/react-context@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-context@npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/156088367de42afa3c7e3acf5f0ba7cad6b359f3d17485585e80c2418434a6ed7cac2602eb73bca265d0091a1ad380f9405c069f103983e53497097ff35ba8f2
  languageName: node
  linkType: hard

"@radix-ui/react-dialog@npm:1.1.5":
  version: 1.1.5
  resolution: "@radix-ui/react-dialog@npm:1.1.5"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-dismissable-layer": "npm:1.1.4"
    "@radix-ui/react-focus-guards": "npm:1.1.1"
    "@radix-ui/react-focus-scope": "npm:1.1.1"
    "@radix-ui/react-id": "npm:1.1.0"
    "@radix-ui/react-portal": "npm:1.1.3"
    "@radix-ui/react-presence": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-slot": "npm:1.1.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
    aria-hidden: "npm:^1.2.4"
    react-remove-scroll: "npm:^2.6.2"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/d426cedcf6c92dff722a8ba75b2e8eed1c85123d7f92c21dd372460c885cb36555f3bce14344dc00622ac5d7c42a2d3918db2d065a6232adb5efc306def5511b
  languageName: node
  linkType: hard

"@radix-ui/react-dialog@npm:^1.1.1":
  version: 1.1.13
  resolution: "@radix-ui/react-dialog@npm:1.1.13"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.2"
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-context": "npm:1.1.2"
    "@radix-ui/react-dismissable-layer": "npm:1.1.9"
    "@radix-ui/react-focus-guards": "npm:1.1.2"
    "@radix-ui/react-focus-scope": "npm:1.1.6"
    "@radix-ui/react-id": "npm:1.1.1"
    "@radix-ui/react-portal": "npm:1.1.8"
    "@radix-ui/react-presence": "npm:1.1.4"
    "@radix-ui/react-primitive": "npm:2.1.2"
    "@radix-ui/react-slot": "npm:1.2.2"
    "@radix-ui/react-use-controllable-state": "npm:1.2.2"
    aria-hidden: "npm:^1.2.4"
    react-remove-scroll: "npm:^2.6.3"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/a9dcd27369c71d1912def6721b5cf2e55f8b4f836340b85abb358edd271137aa19b1aa518a149d8dfbc362368bfe629921aa4f04119ed248a8e1ba3a500d79fe
  languageName: node
  linkType: hard

"@radix-ui/react-direction@npm:1.1.0":
  version: 1.1.0
  resolution: "@radix-ui/react-direction@npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/25ad0d1d65ad08c93cebfbefdff9ef2602e53f4573a66b37d2c366ede9485e75ec6fc8e7dd7d2939b34ea5504ca0fe6ac4a3acc2f6ee9b62d131d65486eafd49
  languageName: node
  linkType: hard

"@radix-ui/react-dismissable-layer@npm:1.1.4":
  version: 1.1.4
  resolution: "@radix-ui/react-dismissable-layer@npm:1.1.4"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-callback-ref": "npm:1.1.0"
    "@radix-ui/react-use-escape-keydown": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/ceaa583b852a81561b216005a30ec029167f4b4ef1ee37aa5782149909c9f1c65288638584f9afa6982d2be81803860600a8c5f07a2c1842c136a76ce2430c75
  languageName: node
  linkType: hard

"@radix-ui/react-dismissable-layer@npm:1.1.9":
  version: 1.1.9
  resolution: "@radix-ui/react-dismissable-layer@npm:1.1.9"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.2"
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.1.2"
    "@radix-ui/react-use-callback-ref": "npm:1.1.1"
    "@radix-ui/react-use-escape-keydown": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/8dc810b75e7cbba10a8d085b16b189e5a6fcdaea38404a1cda643806f32531990052ac95152f1dd97952dc4f8f6f8e66989b5e8d6d451a3920c593c49df1c83a
  languageName: node
  linkType: hard

"@radix-ui/react-dropdown-menu@npm:2.1.5":
  version: 2.1.5
  resolution: "@radix-ui/react-dropdown-menu@npm:2.1.5"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-id": "npm:1.1.0"
    "@radix-ui/react-menu": "npm:2.1.5"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/951bff9909b3b96a7d3dbddd20abb2a6210dcec16773c5e92f86531f5e763c29b1942568d56a02871df18a2dd627d5f150a464e330daf553a406efa6a1b09f62
  languageName: node
  linkType: hard

"@radix-ui/react-focus-guards@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-focus-guards@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/ac8dd31f48fa0500bafd9368f2f06c5a06918dccefa89fa5dc77ca218dc931a094a81ca57f6b181138029822f7acdd5280dceccf5ba4d9263c754fb8f7961879
  languageName: node
  linkType: hard

"@radix-ui/react-focus-guards@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-focus-guards@npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/618658e2b98575198b94ccfdd27f41beb37f83721c9a04617e848afbc47461124ae008d703d713b9644771d96d4852e49de322cf4be3b5f10a4f94d200db5248
  languageName: node
  linkType: hard

"@radix-ui/react-focus-scope@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-focus-scope@npm:1.1.1"
  dependencies:
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-callback-ref": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/128508e7e34a47fd44d51bdb3d66a35a337c54b64125548d4a98bb377ee89b2fd8f96e0a075368d393c6664abba1e5a2f167734a6adbb170c41da0aa7a06d05f
  languageName: node
  linkType: hard

"@radix-ui/react-focus-scope@npm:1.1.6":
  version: 1.1.6
  resolution: "@radix-ui/react-focus-scope@npm:1.1.6"
  dependencies:
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.1.2"
    "@radix-ui/react-use-callback-ref": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/6abb55735577e9f9f4e0eb17355168982f38b51d83de6973ee2fbb232843158ffdbc1dbc5f77d117445811f44bf37394c6ecc79ebd8021b61f50032d85877c2d
  languageName: node
  linkType: hard

"@radix-ui/react-form@npm:0.1.1":
  version: 0.1.1
  resolution: "@radix-ui/react-form@npm:0.1.1"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-id": "npm:1.1.0"
    "@radix-ui/react-label": "npm:2.1.1"
    "@radix-ui/react-primitive": "npm:2.0.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/c504b82a1f5347ec25e9b4ac102b1de7ceee1d2b5392f25a262c2f3f707324742868d65378eb25e3d45352f434be729d94c4ee9ebd88b9babc9cd8f83fe26db2
  languageName: node
  linkType: hard

"@radix-ui/react-hover-card@npm:1.1.5":
  version: 1.1.5
  resolution: "@radix-ui/react-hover-card@npm:1.1.5"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-dismissable-layer": "npm:1.1.4"
    "@radix-ui/react-popper": "npm:1.2.1"
    "@radix-ui/react-portal": "npm:1.1.3"
    "@radix-ui/react-presence": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/011d56f824fbfa94db9d79de359c18aba67091730368af239875fde01d6ee81b7da1fbed8c7614e3dfbdf5df26fc7b645a43801366a87e5d3d9593ee19be7059
  languageName: node
  linkType: hard

"@radix-ui/react-id@npm:1.1.0":
  version: 1.1.0
  resolution: "@radix-ui/react-id@npm:1.1.0"
  dependencies:
    "@radix-ui/react-use-layout-effect": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/6fbc9d1739b3b082412da10359e63967b4f3a60383ebda4c9e56b07a722d29bee53b203b3b1418f88854a29315a7715867133bb149e6e22a027a048cdd20d970
  languageName: node
  linkType: hard

"@radix-ui/react-id@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-id@npm:1.1.1"
  dependencies:
    "@radix-ui/react-use-layout-effect": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/8d68e200778eb3038906870fc869b3d881f4a46715fb20cddd9c76cba42fdaaa4810a3365b6ec2daf0f185b9201fc99d009167f59c7921bc3a139722c2e976db
  languageName: node
  linkType: hard

"@radix-ui/react-label@npm:2.1.1":
  version: 2.1.1
  resolution: "@radix-ui/react-label@npm:2.1.1"
  dependencies:
    "@radix-ui/react-primitive": "npm:2.0.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/40525096bf6fb5a1cfbf5c87aa46fd13d1329155fa62a42e35b4b55db5559d42081b97e77bf5f7ac45064fdae947ee6f78abb1f55b18821760a5712492243e6d
  languageName: node
  linkType: hard

"@radix-ui/react-menu@npm:2.1.5":
  version: 2.1.5
  resolution: "@radix-ui/react-menu@npm:2.1.5"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-collection": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-direction": "npm:1.1.0"
    "@radix-ui/react-dismissable-layer": "npm:1.1.4"
    "@radix-ui/react-focus-guards": "npm:1.1.1"
    "@radix-ui/react-focus-scope": "npm:1.1.1"
    "@radix-ui/react-id": "npm:1.1.0"
    "@radix-ui/react-popper": "npm:1.2.1"
    "@radix-ui/react-portal": "npm:1.1.3"
    "@radix-ui/react-presence": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-roving-focus": "npm:1.1.1"
    "@radix-ui/react-slot": "npm:1.1.1"
    "@radix-ui/react-use-callback-ref": "npm:1.1.0"
    aria-hidden: "npm:^1.2.4"
    react-remove-scroll: "npm:^2.6.2"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/91e62154aecf9a8bcd05349df33affcd163e768b68e4bab1195a8119ee7655f775edecdb2459ef074731cb1bd59843b0b58a6f7b01e93ff745d0f7c6fc9ed70b
  languageName: node
  linkType: hard

"@radix-ui/react-menubar@npm:1.1.5":
  version: 1.1.5
  resolution: "@radix-ui/react-menubar@npm:1.1.5"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-collection": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-direction": "npm:1.1.0"
    "@radix-ui/react-id": "npm:1.1.0"
    "@radix-ui/react-menu": "npm:2.1.5"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-roving-focus": "npm:1.1.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/8b1e1496582b83ff05471aed1f150bfc3bf4dabe64cb0aed79cc03adbff6ffc43eb89995d0572b5ddba49abfe27c8894416d2378077d2a82749ce4ae3a1b6890
  languageName: node
  linkType: hard

"@radix-ui/react-navigation-menu@npm:1.2.4":
  version: 1.2.4
  resolution: "@radix-ui/react-navigation-menu@npm:1.2.4"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-collection": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-direction": "npm:1.1.0"
    "@radix-ui/react-dismissable-layer": "npm:1.1.4"
    "@radix-ui/react-id": "npm:1.1.0"
    "@radix-ui/react-presence": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-callback-ref": "npm:1.1.0"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
    "@radix-ui/react-use-layout-effect": "npm:1.1.0"
    "@radix-ui/react-use-previous": "npm:1.1.0"
    "@radix-ui/react-visually-hidden": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/82136eed0f6ee0bed6bb3863a84b0df31cbfd7724b9d1daea05ce502716570ac4f00c41bf4f7b304f67f05ccaad712f87aa1ee27afabd6341dca9d3dfd06237e
  languageName: node
  linkType: hard

"@radix-ui/react-popover@npm:1.1.5":
  version: 1.1.5
  resolution: "@radix-ui/react-popover@npm:1.1.5"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-dismissable-layer": "npm:1.1.4"
    "@radix-ui/react-focus-guards": "npm:1.1.1"
    "@radix-ui/react-focus-scope": "npm:1.1.1"
    "@radix-ui/react-id": "npm:1.1.0"
    "@radix-ui/react-popper": "npm:1.2.1"
    "@radix-ui/react-portal": "npm:1.1.3"
    "@radix-ui/react-presence": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-slot": "npm:1.1.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
    aria-hidden: "npm:^1.2.4"
    react-remove-scroll: "npm:^2.6.2"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/548921d2f647742c46bc1d4adae0885252034e5b5ebe1714b56562fce74630615da047568ee8a9ae2ebba224dba8663ae21128e9a1d2b59badbe7bcbcf2dd136
  languageName: node
  linkType: hard

"@radix-ui/react-popper@npm:1.2.1":
  version: 1.2.1
  resolution: "@radix-ui/react-popper@npm:1.2.1"
  dependencies:
    "@floating-ui/react-dom": "npm:^2.0.0"
    "@radix-ui/react-arrow": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-callback-ref": "npm:1.1.0"
    "@radix-ui/react-use-layout-effect": "npm:1.1.0"
    "@radix-ui/react-use-rect": "npm:1.1.0"
    "@radix-ui/react-use-size": "npm:1.1.0"
    "@radix-ui/rect": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/423506c2f862c3ee69956bdef3de668bf189b1ec4496c83bef01c3a962c88ab44f9154523afdcd4f0ed6a06eeb44005fcfca4ee0d68267187f58df1f65781b3c
  languageName: node
  linkType: hard

"@radix-ui/react-portal@npm:1.1.3":
  version: 1.1.3
  resolution: "@radix-ui/react-portal@npm:1.1.3"
  dependencies:
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-layout-effect": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/470fb50c940772d05cc268e219b3d15848909dcd0a2dc1952965d0af905992f0ccab99e99c490dea6564c441397eba720b8425ba9f4582c94bef40ebe27ac0d0
  languageName: node
  linkType: hard

"@radix-ui/react-portal@npm:1.1.8":
  version: 1.1.8
  resolution: "@radix-ui/react-portal@npm:1.1.8"
  dependencies:
    "@radix-ui/react-primitive": "npm:2.1.2"
    "@radix-ui/react-use-layout-effect": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/09b573dc678f4aee26cf48e543456b5413befdebde28f089efc1076a8b0f5b58f5f0fab484de01369689be5c264d0b10cf0d7e1cf21cf3bffc8bb9238c9fdc56
  languageName: node
  linkType: hard

"@radix-ui/react-presence@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-presence@npm:1.1.2"
  dependencies:
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-use-layout-effect": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/b7c7a1eed6e2a4b8778f37d925bca12fccb2a3fdd48fa854cb3d6308592aec7253b0a193cba65b8c323e14a14119935434e8f6d9bdc0fbf97450c0da1b4eb0f9
  languageName: node
  linkType: hard

"@radix-ui/react-presence@npm:1.1.4":
  version: 1.1.4
  resolution: "@radix-ui/react-presence@npm:1.1.4"
  dependencies:
    "@radix-ui/react-compose-refs": "npm:1.1.2"
    "@radix-ui/react-use-layout-effect": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/ba01f385f6beedba7bf50ffd4aca8091554a67aee2b7252605876136155ceb2fcf1b627dccaf2e49032231eda271fe0e8915040729da9d1f95d08b854d815305
  languageName: node
  linkType: hard

"@radix-ui/react-primitive@npm:2.0.1":
  version: 2.0.1
  resolution: "@radix-ui/react-primitive@npm:2.0.1"
  dependencies:
    "@radix-ui/react-slot": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/ed6829b8ff4117cde2c02b14325ff78b7902fe9e8324b9fdbfd11646c5bb703f38711d8da5029ffc873384496481b7d398d0e3c17f7cc287b52fb92fbaf67da2
  languageName: node
  linkType: hard

"@radix-ui/react-primitive@npm:2.1.2":
  version: 2.1.2
  resolution: "@radix-ui/react-primitive@npm:2.1.2"
  dependencies:
    "@radix-ui/react-slot": "npm:1.2.2"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/c561ccc9bf56284a7c59f6cafbf19814f802b1cbcad0a18cc2f97a640e46548e7b2089a4b27ac4c7b48d918f0c02f53ff23aca4a74de5ea1a8f6d4c9f410727b
  languageName: node
  linkType: hard

"@radix-ui/react-progress@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-progress@npm:1.1.1"
  dependencies:
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-primitive": "npm:2.0.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/ac4d0bb8500bd56823e016c85981b072d70878f78de9c69b503f10e7fae1dbedfe93cfcd02b19e79ab325aa06d98090d77dc763468fceedbb821913a7105395a
  languageName: node
  linkType: hard

"@radix-ui/react-radio-group@npm:1.2.2":
  version: 1.2.2
  resolution: "@radix-ui/react-radio-group@npm:1.2.2"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-direction": "npm:1.1.0"
    "@radix-ui/react-presence": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-roving-focus": "npm:1.1.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
    "@radix-ui/react-use-previous": "npm:1.1.0"
    "@radix-ui/react-use-size": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/68a8400fad1c1d34d3c9ee3238f465df77a916fd42fa0d6e4f59b510be00dd39da62479881ca3c46b201dce1f06d1c7df9127acd7dd0339eae8aa8ea67ea3a34
  languageName: node
  linkType: hard

"@radix-ui/react-roving-focus@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-roving-focus@npm:1.1.1"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-collection": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-direction": "npm:1.1.0"
    "@radix-ui/react-id": "npm:1.1.0"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-callback-ref": "npm:1.1.0"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/748dc87dfed43243e29be1d0e10a1197249379e5e2bfcd3e1c2b2d2e794f862972befc3192372fb2319a601a7449358e80c9ae8892ebdc58a83688327dcf66d5
  languageName: node
  linkType: hard

"@radix-ui/react-scroll-area@npm:1.2.2":
  version: 1.2.2
  resolution: "@radix-ui/react-scroll-area@npm:1.2.2"
  dependencies:
    "@radix-ui/number": "npm:1.1.0"
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-direction": "npm:1.1.0"
    "@radix-ui/react-presence": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-callback-ref": "npm:1.1.0"
    "@radix-ui/react-use-layout-effect": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/b9a5fd51b2c23ab3dc5ab737c02e0c9b568bc3090196465f872c79e789f10f4452a51f4c1cb060ed13ee9fd8f58822847578be31e585627324eea192bd2c8f9f
  languageName: node
  linkType: hard

"@radix-ui/react-select@npm:2.1.5":
  version: 2.1.5
  resolution: "@radix-ui/react-select@npm:2.1.5"
  dependencies:
    "@radix-ui/number": "npm:1.1.0"
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-collection": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-direction": "npm:1.1.0"
    "@radix-ui/react-dismissable-layer": "npm:1.1.4"
    "@radix-ui/react-focus-guards": "npm:1.1.1"
    "@radix-ui/react-focus-scope": "npm:1.1.1"
    "@radix-ui/react-id": "npm:1.1.0"
    "@radix-ui/react-popper": "npm:1.2.1"
    "@radix-ui/react-portal": "npm:1.1.3"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-slot": "npm:1.1.1"
    "@radix-ui/react-use-callback-ref": "npm:1.1.0"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
    "@radix-ui/react-use-layout-effect": "npm:1.1.0"
    "@radix-ui/react-use-previous": "npm:1.1.0"
    "@radix-ui/react-visually-hidden": "npm:1.1.1"
    aria-hidden: "npm:^1.2.4"
    react-remove-scroll: "npm:^2.6.2"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/651e0058db331511e02986590bae7aa9dc5c1deaddf204a486f03682405a418a0a3f484b44c4a3daf1dd98f271838d4d6d23e0d092ad646ebf40393d1a8c3f43
  languageName: node
  linkType: hard

"@radix-ui/react-separator@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-separator@npm:1.1.1"
  dependencies:
    "@radix-ui/react-primitive": "npm:2.0.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/85de387e6b94548d777b5ebafe3f170eb0bab31ec1850ee8bc58f7fc48b725d6cf33c4fb2c92be3196ca843a14819fa32108dd78d5675994e31adb5500484e01
  languageName: node
  linkType: hard

"@radix-ui/react-slider@npm:1.2.2":
  version: 1.2.2
  resolution: "@radix-ui/react-slider@npm:1.2.2"
  dependencies:
    "@radix-ui/number": "npm:1.1.0"
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-collection": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-direction": "npm:1.1.0"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
    "@radix-ui/react-use-layout-effect": "npm:1.1.0"
    "@radix-ui/react-use-previous": "npm:1.1.0"
    "@radix-ui/react-use-size": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/1a4c01c13af6ba76fd4e659686b0dedce65a6e6fd9bbc2f411b53bd4fb5b86879f3108f9f373e06b9b84f1854dd24c60714d1e581789b259e8f4a6e28bea2cac
  languageName: node
  linkType: hard

"@radix-ui/react-slot@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-slot@npm:1.1.1"
  dependencies:
    "@radix-ui/react-compose-refs": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/5b1ee5100da356c8f9f56cd7ca273838a373fa3808f0f909b1e132c4f734282571cb666e86a548831ee82a62240e126d43379994285a9b030fd34ea43538b5e2
  languageName: node
  linkType: hard

"@radix-ui/react-slot@npm:1.2.2":
  version: 1.2.2
  resolution: "@radix-ui/react-slot@npm:1.2.2"
  dependencies:
    "@radix-ui/react-compose-refs": "npm:1.1.2"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/8014d9d9a5c215d913af8b54a1f60fb336b7c848329d7339968adff6071e2a7c159878fffb777d51d1c30844d4a1575b3fe48810b694e57d5a9c2fdff0da62f5
  languageName: node
  linkType: hard

"@radix-ui/react-switch@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-switch@npm:1.1.2"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
    "@radix-ui/react-use-previous": "npm:1.1.0"
    "@radix-ui/react-use-size": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/ebe94a10917d370951cd988b7f16dcda62f705d43b87422adbd984e16eab00ed052960dbb71c9f87e26d8dddf11adacf5dc44e92414f36cbcf911de83ed6e6c0
  languageName: node
  linkType: hard

"@radix-ui/react-tabs@npm:1.1.2":
  version: 1.1.2
  resolution: "@radix-ui/react-tabs@npm:1.1.2"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-direction": "npm:1.1.0"
    "@radix-ui/react-id": "npm:1.1.0"
    "@radix-ui/react-presence": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-roving-focus": "npm:1.1.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/b4b160399408918412edc35be50deb22433808fcefb776e158cc0fc534bc558cf73104191f6ee757f024a37320bed263d91608d1d83fd514019a1f18cc844ef7
  languageName: node
  linkType: hard

"@radix-ui/react-toast@npm:1.2.5":
  version: 1.2.5
  resolution: "@radix-ui/react-toast@npm:1.2.5"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-collection": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-dismissable-layer": "npm:1.1.4"
    "@radix-ui/react-portal": "npm:1.1.3"
    "@radix-ui/react-presence": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-callback-ref": "npm:1.1.0"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
    "@radix-ui/react-use-layout-effect": "npm:1.1.0"
    "@radix-ui/react-visually-hidden": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/289718070b026a1efaef633286d486b800ede72edc50f373e16a200bf07b14a6ce629ab06014c85c98e386822167ac22eebdec029730d03b753ee507b317f710
  languageName: node
  linkType: hard

"@radix-ui/react-toggle-group@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-toggle-group@npm:1.1.1"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-direction": "npm:1.1.0"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-roving-focus": "npm:1.1.1"
    "@radix-ui/react-toggle": "npm:1.1.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/4a1c198d48419afae1584c892c45c53f1916a00e0b9a9fa9bb123bbd481c6df859fdfd5768c65566ed033b78e1256909d7dac12aeb3212f57743a6b3b75c89b0
  languageName: node
  linkType: hard

"@radix-ui/react-toggle@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-toggle@npm:1.1.1"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/08be2f038f1756c24267f5af59ba14fbf49df11bba4bb8d5f7165c11b83e5a75277cee76a1bb92c50be75767ae0e994a2db82d853da54933e021dcfabe95ddfc
  languageName: node
  linkType: hard

"@radix-ui/react-toolbar@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-toolbar@npm:1.1.1"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-direction": "npm:1.1.0"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-roving-focus": "npm:1.1.1"
    "@radix-ui/react-separator": "npm:1.1.1"
    "@radix-ui/react-toggle-group": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/f22e3ae1531bd28fb3a8df232bd8c57190aec1a1171bf5a322584f9f6afb396c5f25e5a4bdfd635894c6c2cce98f1362a84d00e5248dbdee92bceb5603cfb20e
  languageName: node
  linkType: hard

"@radix-ui/react-tooltip@npm:1.1.7":
  version: 1.1.7
  resolution: "@radix-ui/react-tooltip@npm:1.1.7"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-dismissable-layer": "npm:1.1.4"
    "@radix-ui/react-id": "npm:1.1.0"
    "@radix-ui/react-popper": "npm:1.2.1"
    "@radix-ui/react-portal": "npm:1.1.3"
    "@radix-ui/react-presence": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-slot": "npm:1.1.1"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
    "@radix-ui/react-visually-hidden": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/4ecf502900f54ba564ddc77726821847ae66349bbc216036aa06865af9d7cadeb0686d5a1274b54036661451b78d87405c296c045896c48092a08906abea3c2f
  languageName: node
  linkType: hard

"@radix-ui/react-use-callback-ref@npm:1.1.0":
  version: 1.1.0
  resolution: "@radix-ui/react-use-callback-ref@npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/2ec7903c67e3034b646005556f44fd975dc5204db6885fc58403e3584f27d95f0b573bc161de3d14fab9fda25150bf3b91f718d299fdfc701c736bd0bd2281fa
  languageName: node
  linkType: hard

"@radix-ui/react-use-callback-ref@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-callback-ref@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/cde8c40f1d4e79e6e71470218163a746858304bad03758ac84dc1f94247a046478e8e397518350c8d6609c84b7e78565441d7505bb3ed573afce82cfdcd19faf
  languageName: node
  linkType: hard

"@radix-ui/react-use-controllable-state@npm:1.1.0":
  version: 1.1.0
  resolution: "@radix-ui/react-use-controllable-state@npm:1.1.0"
  dependencies:
    "@radix-ui/react-use-callback-ref": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/9583679150dc521c9de20ee22cb858697dd4f5cefc46ab8ebfc5e7511415a053994e87d4ca3f49de84d27eebc13535b0a6c9892c91ab43e3e553e5d7270f378f
  languageName: node
  linkType: hard

"@radix-ui/react-use-controllable-state@npm:1.2.2":
  version: 1.2.2
  resolution: "@radix-ui/react-use-controllable-state@npm:1.2.2"
  dependencies:
    "@radix-ui/react-use-effect-event": "npm:0.0.2"
    "@radix-ui/react-use-layout-effect": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/a100bff3ddecb753dab17444147273c9f70046c5949712c52174b259622eaef12acbf7ebcf289bae4e714eb84d0a7317c1aa44064cd997f327d77b62bc732a7c
  languageName: node
  linkType: hard

"@radix-ui/react-use-effect-event@npm:0.0.2":
  version: 0.0.2
  resolution: "@radix-ui/react-use-effect-event@npm:0.0.2"
  dependencies:
    "@radix-ui/react-use-layout-effect": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/5a1950a30a399ea7e4b98154da9f536737a610de80189b7aacd4f064a89a3cd0d2a48571d527435227252e72e872bdb544ff6ffcfbdd02de2efd011be4aaa902
  languageName: node
  linkType: hard

"@radix-ui/react-use-escape-keydown@npm:1.1.0":
  version: 1.1.0
  resolution: "@radix-ui/react-use-escape-keydown@npm:1.1.0"
  dependencies:
    "@radix-ui/react-use-callback-ref": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/9bf88ea272b32ea0f292afd336780a59c5646f795036b7e6105df2d224d73c54399ee5265f61d571eb545d28382491a8b02dc436e3088de8dae415d58b959b71
  languageName: node
  linkType: hard

"@radix-ui/react-use-escape-keydown@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-escape-keydown@npm:1.1.1"
  dependencies:
    "@radix-ui/react-use-callback-ref": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/0eb0756c2c55ddcde9ff01446ab01c085ab2bf799173e97db7ef5f85126f9e8600225570801a1f64740e6d14c39ffe8eed7c14d29737345a5797f4622ac96f6f
  languageName: node
  linkType: hard

"@radix-ui/react-use-layout-effect@npm:1.1.0":
  version: 1.1.0
  resolution: "@radix-ui/react-use-layout-effect@npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/271ea0bf1cd74718895a68414a6e95537737f36e02ad08eeb61a82b229d6abda9cff3135a479e134e1f0ce2c3ff97bb85babbdce751985fb755a39b231d7ccf2
  languageName: node
  linkType: hard

"@radix-ui/react-use-layout-effect@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-use-layout-effect@npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/bad2ba4f206e6255263582bedfb7868773c400836f9a1b423c0b464ffe4a17e13d3f306d1ce19cf7a19a492e9d0e49747464f2656451bb7c6a99f5a57bd34de2
  languageName: node
  linkType: hard

"@radix-ui/react-use-previous@npm:1.1.0":
  version: 1.1.0
  resolution: "@radix-ui/react-use-previous@npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/8a2407e3db6248ab52bf425f5f4161355d09f1a228038094959250ae53552e73543532b3bb80e452f6ad624621e2e1c6aebb8c702f2dfaa5e89f07ec629d9304
  languageName: node
  linkType: hard

"@radix-ui/react-use-rect@npm:1.1.0":
  version: 1.1.0
  resolution: "@radix-ui/react-use-rect@npm:1.1.0"
  dependencies:
    "@radix-ui/rect": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/facc9528af43df3b01952dbb915ff751b5924db2c31d41f053ddea19a7cc5cac5b096c4d7a2059e8f564a3f0d4a95bcd909df8faed52fa01709af27337628e2c
  languageName: node
  linkType: hard

"@radix-ui/react-use-size@npm:1.1.0":
  version: 1.1.0
  resolution: "@radix-ui/react-use-size@npm:1.1.0"
  dependencies:
    "@radix-ui/react-use-layout-effect": "npm:1.1.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/01a11d4c07fc620b8a081e53d7ec8495b19a11e02688f3d9f47cf41a5fe0428d1e52ed60b2bf88dfd447dc2502797b9dad2841097389126dd108530913c4d90d
  languageName: node
  linkType: hard

"@radix-ui/react-visually-hidden@npm:1.1.1":
  version: 1.1.1
  resolution: "@radix-ui/react-visually-hidden@npm:1.1.1"
  dependencies:
    "@radix-ui/react-primitive": "npm:2.0.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/ccbdf29811283fb257f0b0f8604923e6fe349a264986463f6d6a20946fc51e243527985e69f0af27659f78fd7a4199dacbba5bfc7af3667aa409cd23a0ae3283
  languageName: node
  linkType: hard

"@radix-ui/rect@npm:1.1.0":
  version: 1.1.0
  resolution: "@radix-ui/rect@npm:1.1.0"
  checksum: 10/3ffdc5e3f7bcd91de4d5983513bd11c3a82b89b966e5c1bd8c17690a8f5da2d83fa156474c7b68fc6b9465df2281f81983b146e1d9dc57d332abda05751a9cbc
  languageName: node
  linkType: hard

"@react-aria/breadcrumbs@npm:^3.5.23":
  version: 3.5.23
  resolution: "@react-aria/breadcrumbs@npm:3.5.23"
  dependencies:
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/link": "npm:^3.8.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/breadcrumbs": "npm:^3.7.12"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/2a6038e1e381e1971d5fc2b5c7621f0cab5de0f09cd946d0e977b682e3e3caf0306e062d35bc814f666efa9ef9366742a03928f9a506a273dfbaae6df6b53662
  languageName: node
  linkType: hard

"@react-aria/button@npm:^3.13.0":
  version: 3.13.0
  resolution: "@react-aria/button@npm:3.13.0"
  dependencies:
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/toolbar": "npm:3.0.0-beta.15"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/toggle": "npm:^3.8.3"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/a1d5b5b6797b8a08039f8343309a34a8237aa66ea22f5d9938794b50a411b332e8aa9c5009d2ccf732a4d6553ca4e5dbef3efae25fc55b6b0f2bc177339bd1b0
  languageName: node
  linkType: hard

"@react-aria/calendar@npm:^3.8.0":
  version: 3.8.0
  resolution: "@react-aria/calendar@npm:3.8.0"
  dependencies:
    "@internationalized/date": "npm:^3.8.0"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/live-announcer": "npm:^3.4.2"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/calendar": "npm:^3.8.0"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/calendar": "npm:^3.7.0"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/998d40ace20f5fd2d34da65927432f4c26203f75130561171164fc489852758a7ac36b5115e4236a38173679c231ad20030a7543293b7a21123d49098ebc5794
  languageName: node
  linkType: hard

"@react-aria/checkbox@npm:^3.15.4":
  version: 3.15.4
  resolution: "@react-aria/checkbox@npm:3.15.4"
  dependencies:
    "@react-aria/form": "npm:^3.0.15"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/label": "npm:^3.7.17"
    "@react-aria/toggle": "npm:^3.11.2"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/checkbox": "npm:^3.6.13"
    "@react-stately/form": "npm:^3.1.3"
    "@react-stately/toggle": "npm:^3.8.3"
    "@react-types/checkbox": "npm:^3.9.3"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/5a803d76d400fd6cd4dc9d865550632b2ca8c41c2869225f7738fe02ec41779963afa500d833c80e351c6de2ef6ba9b840d5a44cf0157c9d9ab9f57a6324f81e
  languageName: node
  linkType: hard

"@react-aria/color@npm:^3.0.6":
  version: 3.0.6
  resolution: "@react-aria/color@npm:3.0.6"
  dependencies:
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/numberfield": "npm:^3.11.13"
    "@react-aria/slider": "npm:^3.7.18"
    "@react-aria/spinbutton": "npm:^3.6.14"
    "@react-aria/textfield": "npm:^3.17.2"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-aria/visually-hidden": "npm:^3.8.22"
    "@react-stately/color": "npm:^3.8.4"
    "@react-stately/form": "npm:^3.1.3"
    "@react-types/color": "npm:^3.0.4"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/c38a707c70b7c56c101b63e4f43bdc28dd705022646469e09a37d4641e20675da7be18168b906ad5ed1673fab88a625aa9517cc81e93c65d8cd1af4e07e52c53
  languageName: node
  linkType: hard

"@react-aria/combobox@npm:^3.12.2":
  version: 3.12.2
  resolution: "@react-aria/combobox@npm:3.12.2"
  dependencies:
    "@react-aria/focus": "npm:^3.20.2"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/listbox": "npm:^3.14.3"
    "@react-aria/live-announcer": "npm:^3.4.2"
    "@react-aria/menu": "npm:^3.18.2"
    "@react-aria/overlays": "npm:^3.27.0"
    "@react-aria/selection": "npm:^3.24.0"
    "@react-aria/textfield": "npm:^3.17.2"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/collections": "npm:^3.12.3"
    "@react-stately/combobox": "npm:^3.10.4"
    "@react-stately/form": "npm:^3.1.3"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/combobox": "npm:^3.13.4"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/876779ecd1742e7125c60ad1fb26bf7d20dd3b9e86deeead747e8db1f7e4fd64f9d556ec8632a6f78cd3c46a575e4597742938bacfc7b4345f4fb28404afd2d8
  languageName: node
  linkType: hard

"@react-aria/datepicker@npm:^3.14.2":
  version: 3.14.2
  resolution: "@react-aria/datepicker@npm:3.14.2"
  dependencies:
    "@internationalized/date": "npm:^3.8.0"
    "@internationalized/number": "npm:^3.6.1"
    "@internationalized/string": "npm:^3.2.6"
    "@react-aria/focus": "npm:^3.20.2"
    "@react-aria/form": "npm:^3.0.15"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/label": "npm:^3.7.17"
    "@react-aria/spinbutton": "npm:^3.6.14"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/datepicker": "npm:^3.14.0"
    "@react-stately/form": "npm:^3.1.3"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/calendar": "npm:^3.7.0"
    "@react-types/datepicker": "npm:^3.12.0"
    "@react-types/dialog": "npm:^3.5.17"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/9aadf772f35751ff5957cd1e3eae8c72678bdd31de1c34bd20bf5b6a4f2c016d49354fa57ab610f708a6f15883637765fc2667e081115c5492ef9c71cfba89d5
  languageName: node
  linkType: hard

"@react-aria/dialog@npm:^3.5.24":
  version: 3.5.24
  resolution: "@react-aria/dialog@npm:3.5.24"
  dependencies:
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/overlays": "npm:^3.27.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/dialog": "npm:^3.5.17"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/2250a00497d503f75b1c918eea2919a3f10665972984eb897d6a0799dc3740b2424dd2554b91a8363a0a1bcf16a3fb841c97dab51209750699e54f304be5fed1
  languageName: node
  linkType: hard

"@react-aria/disclosure@npm:^3.0.4":
  version: 3.0.4
  resolution: "@react-aria/disclosure@npm:3.0.4"
  dependencies:
    "@react-aria/ssr": "npm:^3.9.8"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/disclosure": "npm:^3.0.3"
    "@react-types/button": "npm:^3.12.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/b18b5e09b141a7b6d0f130e050b643a11cf794bfa1db18ecbeaaa59341b7c660d6d80f54eadd51006269cdbe2d5cc891047f472664f072b6fee665e68b82415b
  languageName: node
  linkType: hard

"@react-aria/dnd@npm:^3.9.2":
  version: 3.9.2
  resolution: "@react-aria/dnd@npm:3.9.2"
  dependencies:
    "@internationalized/string": "npm:^3.2.6"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/live-announcer": "npm:^3.4.2"
    "@react-aria/overlays": "npm:^3.27.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/dnd": "npm:^3.5.3"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/ccf66db19d8c2e3b44e066c7299e6c36b064bcafee65ce1893839bbb396a7b3fbc559909609e9f3b46a372504f78521dbdc3625f19b08bdcdfabdd8bdd22727f
  languageName: node
  linkType: hard

"@react-aria/focus@npm:^3.17.1, @react-aria/focus@npm:^3.20.2":
  version: 3.20.2
  resolution: "@react-aria/focus@npm:3.20.2"
  dependencies:
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
    clsx: "npm:^2.0.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/da3ba2d38aa20c855b1c4240f099f7e9be628eca2daf60f529920e3881513d7fa0f989e6dba081c3de9cbe4cccbfa70c1cd6c44deb9657f76979cf00271d3ac6
  languageName: node
  linkType: hard

"@react-aria/form@npm:^3.0.15":
  version: 3.0.15
  resolution: "@react-aria/form@npm:3.0.15"
  dependencies:
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/form": "npm:^3.1.3"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/4a8678d3bf7af9dea3a7abe6595823d6a8f55661be48c2f4f4bbbaabf94bda84536e76011daf23561fd2b6a3a79bfe3541b46017ad32d0f4acdf63f579b1feb6
  languageName: node
  linkType: hard

"@react-aria/grid@npm:^3.13.0":
  version: 3.13.0
  resolution: "@react-aria/grid@npm:3.13.0"
  dependencies:
    "@react-aria/focus": "npm:^3.20.2"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/live-announcer": "npm:^3.4.2"
    "@react-aria/selection": "npm:^3.24.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/collections": "npm:^3.12.3"
    "@react-stately/grid": "npm:^3.11.1"
    "@react-stately/selection": "npm:^3.20.1"
    "@react-types/checkbox": "npm:^3.9.3"
    "@react-types/grid": "npm:^3.3.1"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/9b446d9117aa1d74d024c4dc846dce3ad209fcfb77cd519d6914d4d765740ea138a3a2c9110e0149419306c156a64e1ca25d246131424628431ad7210cd006ed
  languageName: node
  linkType: hard

"@react-aria/gridlist@npm:^3.12.0":
  version: 3.12.0
  resolution: "@react-aria/gridlist@npm:3.12.0"
  dependencies:
    "@react-aria/focus": "npm:^3.20.2"
    "@react-aria/grid": "npm:^3.13.0"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/selection": "npm:^3.24.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/collections": "npm:^3.12.3"
    "@react-stately/list": "npm:^3.12.1"
    "@react-stately/tree": "npm:^3.8.9"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/b8a015d016ae88d19705e466ecd544299cddf2f0cdc91193f0f2608ad5bf4ecd43db227ee06855fb61ee0a638b553c37198d286b658d63947cd94676b098910b
  languageName: node
  linkType: hard

"@react-aria/i18n@npm:^3.12.8":
  version: 3.12.8
  resolution: "@react-aria/i18n@npm:3.12.8"
  dependencies:
    "@internationalized/date": "npm:^3.8.0"
    "@internationalized/message": "npm:^3.1.7"
    "@internationalized/number": "npm:^3.6.1"
    "@internationalized/string": "npm:^3.2.6"
    "@react-aria/ssr": "npm:^3.9.8"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/5aa307044f077240db407d834d80445806772d40fd4c460ac037a9683651b3f493a06dc19b2fc20f1e7bb64e3695505aefa9cfcc2ccdcc99595fbf599fd1358d
  languageName: node
  linkType: hard

"@react-aria/interactions@npm:^3.21.3, @react-aria/interactions@npm:^3.25.0":
  version: 3.25.0
  resolution: "@react-aria/interactions@npm:3.25.0"
  dependencies:
    "@react-aria/ssr": "npm:^3.9.8"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/flags": "npm:^3.1.1"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/9bb67f9560b504f15e26077a0bff4b1571f66769afeb98d46007df183b1ff171c216488664bbae3a3a34470e7c9c666382bab691ef9bd591fb34630d88473f28
  languageName: node
  linkType: hard

"@react-aria/label@npm:^3.7.17":
  version: 3.7.17
  resolution: "@react-aria/label@npm:3.7.17"
  dependencies:
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/d829bf0b77931923ead1eb3616c08ad7d3159fd7aed37478797f60426d415bef17f3add76298cb3f400cbebbd9921592b4a8c8fe2768c747b0c0ec37a5bde7b3
  languageName: node
  linkType: hard

"@react-aria/landmark@npm:^3.0.2":
  version: 3.0.2
  resolution: "@react-aria/landmark@npm:3.0.2"
  dependencies:
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
    use-sync-external-store: "npm:^1.4.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/ae63bc7098911a5e382bc7aa4ed403711e4bd6ab6a65cad2f96d54abebecc0623721e1a01b419aa896445795e04b5c0db1648b68fa2c3522a679d93abe2f7716
  languageName: node
  linkType: hard

"@react-aria/link@npm:^3.8.0":
  version: 3.8.0
  resolution: "@react-aria/link@npm:3.8.0"
  dependencies:
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/link": "npm:^3.6.0"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/d8558d1fe8f3ae340f4bc059a79960709867f1347edb2f4feb0bcd406f1b092a045fcec684b0970ae4eeae1049148180228ccfcbffdeae5e6bd80bc15f7f44ec
  languageName: node
  linkType: hard

"@react-aria/listbox@npm:^3.14.3":
  version: 3.14.3
  resolution: "@react-aria/listbox@npm:3.14.3"
  dependencies:
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/label": "npm:^3.7.17"
    "@react-aria/selection": "npm:^3.24.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/collections": "npm:^3.12.3"
    "@react-stately/list": "npm:^3.12.1"
    "@react-types/listbox": "npm:^3.6.0"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/ab0840e702926599bfc2d66a8475ac22d29c3644c9015b373ba841eed4ac21ceb52c0960c6e1b77c6ae9600f3671f8d2d9eadcf1ee14801aabd3e0367fd2f83f
  languageName: node
  linkType: hard

"@react-aria/live-announcer@npm:^3.4.2":
  version: 3.4.2
  resolution: "@react-aria/live-announcer@npm:3.4.2"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  checksum: 10/e1379ea819fb2b2921126114da141328514e9cd054a89dec1f226ecfebf884319dcb02a04c360261b1cf17a8e0a74822b5fbd3f110025fdf85372db9c4a2fff5
  languageName: node
  linkType: hard

"@react-aria/menu@npm:^3.18.2":
  version: 3.18.2
  resolution: "@react-aria/menu@npm:3.18.2"
  dependencies:
    "@react-aria/focus": "npm:^3.20.2"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/overlays": "npm:^3.27.0"
    "@react-aria/selection": "npm:^3.24.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/collections": "npm:^3.12.3"
    "@react-stately/menu": "npm:^3.9.3"
    "@react-stately/selection": "npm:^3.20.1"
    "@react-stately/tree": "npm:^3.8.9"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/menu": "npm:^3.10.0"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/55b2d773782ca62001116f8876fc60c8965e3042ef6287ddfba38dc3cc60453b191b13ed820c5ff6a1df1e1da42418bf9347599fe132b0272a88e48f8a83f450
  languageName: node
  linkType: hard

"@react-aria/meter@npm:^3.4.22":
  version: 3.4.22
  resolution: "@react-aria/meter@npm:3.4.22"
  dependencies:
    "@react-aria/progress": "npm:^3.4.22"
    "@react-types/meter": "npm:^3.4.8"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/024e37bde8a881fab7baf422bd85d5c5c9a8dfd5a600c80fbfcf3298e84b2917092fd3f05c3cabc304704dd32ccf277e6c3304e10a363405ba9044fb5402c985
  languageName: node
  linkType: hard

"@react-aria/numberfield@npm:^3.11.13":
  version: 3.11.13
  resolution: "@react-aria/numberfield@npm:3.11.13"
  dependencies:
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/spinbutton": "npm:^3.6.14"
    "@react-aria/textfield": "npm:^3.17.2"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/form": "npm:^3.1.3"
    "@react-stately/numberfield": "npm:^3.9.11"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/numberfield": "npm:^3.8.10"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/8be7f2ec51acb8a791e52813c193f5fde94b0ae2ee568dca67aa3596a34a06616d649adea257576b1eddf5aff9e8615b71afa4f1525c6382c44f9cee7d2b88f1
  languageName: node
  linkType: hard

"@react-aria/overlays@npm:^3.27.0":
  version: 3.27.0
  resolution: "@react-aria/overlays@npm:3.27.0"
  dependencies:
    "@react-aria/focus": "npm:^3.20.2"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/ssr": "npm:^3.9.8"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-aria/visually-hidden": "npm:^3.8.22"
    "@react-stately/overlays": "npm:^3.6.15"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/overlays": "npm:^3.8.14"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/34dbe7a95b2a8c5f68229209b134c32b0a425e31c74167b49a495e1209629374e441b4906b8d341da2a5d5262c084ac7e30eb4f8c822c960a090b6d829bfb7fc
  languageName: node
  linkType: hard

"@react-aria/progress@npm:^3.4.22":
  version: 3.4.22
  resolution: "@react-aria/progress@npm:3.4.22"
  dependencies:
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/label": "npm:^3.7.17"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/progress": "npm:^3.5.11"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/12b396674685c7c18a917d331e28d7ede47d4da851c4e9697d5738f57570b071ce9520c3b67875fc3b7918928d6a5f2f63498a56e1219c178a5f48fa16cf0c87
  languageName: node
  linkType: hard

"@react-aria/radio@npm:^3.11.2":
  version: 3.11.2
  resolution: "@react-aria/radio@npm:3.11.2"
  dependencies:
    "@react-aria/focus": "npm:^3.20.2"
    "@react-aria/form": "npm:^3.0.15"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/label": "npm:^3.7.17"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/radio": "npm:^3.10.12"
    "@react-types/radio": "npm:^3.8.8"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/581e40555b0b0ebe76b854dc4daa9fc1393ee8c273db739281935488d1ab846f165403ec5e854ccc89fa40041a307bc20ebd30ca65f2afb12d1b53775a17f508
  languageName: node
  linkType: hard

"@react-aria/searchfield@npm:^3.8.3":
  version: 3.8.3
  resolution: "@react-aria/searchfield@npm:3.8.3"
  dependencies:
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/textfield": "npm:^3.17.2"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/searchfield": "npm:^3.5.11"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/searchfield": "npm:^3.6.1"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/cf793216ae14ad89ce2282107bcde1a717036a0fa8f5a525c64ae54c91ef1a9f4d02deba8ecb2bf9c2ab3ba8912a757d820d1817a73ec66c25aeaecde8d479af
  languageName: node
  linkType: hard

"@react-aria/select@npm:^3.15.4":
  version: 3.15.4
  resolution: "@react-aria/select@npm:3.15.4"
  dependencies:
    "@react-aria/form": "npm:^3.0.15"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/label": "npm:^3.7.17"
    "@react-aria/listbox": "npm:^3.14.3"
    "@react-aria/menu": "npm:^3.18.2"
    "@react-aria/selection": "npm:^3.24.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-aria/visually-hidden": "npm:^3.8.22"
    "@react-stately/select": "npm:^3.6.12"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/select": "npm:^3.9.11"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/00c1cd5f7d39bf68dc87bcccafde2f9a7f4be0d048c3a00d3b21479bd3c36208109f01ff056a4065367ad71df406c84941b121e4fd16bbb2328fea0ece41bb47
  languageName: node
  linkType: hard

"@react-aria/selection@npm:^3.24.0":
  version: 3.24.0
  resolution: "@react-aria/selection@npm:3.24.0"
  dependencies:
    "@react-aria/focus": "npm:^3.20.2"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/selection": "npm:^3.20.1"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/266f4b17eaab9724141ba63c232e62138acad26086bb81c0633ba849c86353881d76c71003f4a17ebe5b11d499adb819f3eba44d621f403cc4cfcd97f4424637
  languageName: node
  linkType: hard

"@react-aria/separator@npm:^3.4.8":
  version: 3.4.8
  resolution: "@react-aria/separator@npm:3.4.8"
  dependencies:
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/ca7b0e0fd3d0b988b94670240ee70c63aba87cd6fe433d54c61dd73d7547900adc5b32eb950ead0d70a159b237ed6da61af43542598bd64be2b92fadb93df62a
  languageName: node
  linkType: hard

"@react-aria/slider@npm:^3.7.18":
  version: 3.7.18
  resolution: "@react-aria/slider@npm:3.7.18"
  dependencies:
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/label": "npm:^3.7.17"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/slider": "npm:^3.6.3"
    "@react-types/shared": "npm:^3.29.0"
    "@react-types/slider": "npm:^3.7.10"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/d27ad9e36cc2f160a73849c288f9e284af574088ad1d2c45a9d8ca36c8537b2258c28c2f2668166ce287f2b45e9f0d4237f9964f0cb651773c62a6e22ceb2352
  languageName: node
  linkType: hard

"@react-aria/spinbutton@npm:^3.6.14":
  version: 3.6.14
  resolution: "@react-aria/spinbutton@npm:3.6.14"
  dependencies:
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/live-announcer": "npm:^3.4.2"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/026622981025bb09fbab262052ce4f936eac890052e0959b91f9a0093513192b0ad940f0177c02508e7165475e6bfe239846f9cfea43dccc340aa9f0aad6bbfd
  languageName: node
  linkType: hard

"@react-aria/ssr@npm:^3.9.8":
  version: 3.9.8
  resolution: "@react-aria/ssr@npm:3.9.8"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/01b40c57146406cb4d8e1aa628ffbcf1ea50521dfd81bbcdad777b87eef865abe01168f0c959e140cabae1f323b522243b77228d9e533d1b1b34de71c886623a
  languageName: node
  linkType: hard

"@react-aria/switch@npm:^3.7.2":
  version: 3.7.2
  resolution: "@react-aria/switch@npm:3.7.2"
  dependencies:
    "@react-aria/toggle": "npm:^3.11.2"
    "@react-stately/toggle": "npm:^3.8.3"
    "@react-types/shared": "npm:^3.29.0"
    "@react-types/switch": "npm:^3.5.10"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/a18e07993b1754f4d2d53b3ecb44f39d22f863e9bc58fcae23fc46ef14147ea096e07c740b2957cf7b2477c04c5df155525e7bb14bb31530198dcf63b2d3f9ea
  languageName: node
  linkType: hard

"@react-aria/table@npm:^3.17.2":
  version: 3.17.2
  resolution: "@react-aria/table@npm:3.17.2"
  dependencies:
    "@react-aria/focus": "npm:^3.20.2"
    "@react-aria/grid": "npm:^3.13.0"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/live-announcer": "npm:^3.4.2"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-aria/visually-hidden": "npm:^3.8.22"
    "@react-stately/collections": "npm:^3.12.3"
    "@react-stately/flags": "npm:^3.1.1"
    "@react-stately/table": "npm:^3.14.1"
    "@react-types/checkbox": "npm:^3.9.3"
    "@react-types/grid": "npm:^3.3.1"
    "@react-types/shared": "npm:^3.29.0"
    "@react-types/table": "npm:^3.12.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/f70c98e8bbefc27c422d9b1ab0e1f87a07cb556ae4f313f6b829f09f4856dfcc922d7b57336ec88c90d8282d03dbff7d4492572762e182ee1740ad60927ed0ce
  languageName: node
  linkType: hard

"@react-aria/tabs@npm:^3.10.2":
  version: 3.10.2
  resolution: "@react-aria/tabs@npm:3.10.2"
  dependencies:
    "@react-aria/focus": "npm:^3.20.2"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/selection": "npm:^3.24.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/tabs": "npm:^3.8.1"
    "@react-types/shared": "npm:^3.29.0"
    "@react-types/tabs": "npm:^3.3.14"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/deea6817f6a1694b601032fb7ba4dc5a9247b15851b471186db2e5112b0ec082dcbd3a43864bfac74221c5f092771d8ecf25139a3f91c3dba1b6e0c046120a63
  languageName: node
  linkType: hard

"@react-aria/tag@npm:^3.5.2":
  version: 3.5.2
  resolution: "@react-aria/tag@npm:3.5.2"
  dependencies:
    "@react-aria/gridlist": "npm:^3.12.0"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/label": "npm:^3.7.17"
    "@react-aria/selection": "npm:^3.24.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/list": "npm:^3.12.1"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/2e6fa6f910fb91c3e34d3ec563ecd29a86bab80c043eedf85f07953dfad6bde8c301e5743d88a1080b76c4b2d40cf0ec5bd3970136a671d8321c759556163463
  languageName: node
  linkType: hard

"@react-aria/textfield@npm:^3.17.2":
  version: 3.17.2
  resolution: "@react-aria/textfield@npm:3.17.2"
  dependencies:
    "@react-aria/form": "npm:^3.0.15"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/label": "npm:^3.7.17"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/form": "npm:^3.1.3"
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/shared": "npm:^3.29.0"
    "@react-types/textfield": "npm:^3.12.1"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/8284232fd3b117a9fbe4db282ba11ccf6154bf89581e04e66c2de56a2ee7e729f029b477741a8e9004d25b018235ca7d64ddc23fee1f35f4e50a145f5da1d308
  languageName: node
  linkType: hard

"@react-aria/toast@npm:^3.0.2":
  version: 3.0.2
  resolution: "@react-aria/toast@npm:3.0.2"
  dependencies:
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/landmark": "npm:^3.0.2"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/toast": "npm:^3.1.0"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/57ed780f37a25676e4ac272e6085dc172b2f50e670c5bc310f817de7b4199958173a38e90b8d057d5c4d680ae279eb94639ddbfbf7e97cf37f2f9bed99b59fb4
  languageName: node
  linkType: hard

"@react-aria/toggle@npm:^3.11.2":
  version: 3.11.2
  resolution: "@react-aria/toggle@npm:3.11.2"
  dependencies:
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/toggle": "npm:^3.8.3"
    "@react-types/checkbox": "npm:^3.9.3"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/058eca67cf3dbcc3b744b262cd95f52cb0d562d4590e217bf5a862d17199172b55fb5bc52611baa373e03dc47fb1a444920770436accf9caf0e793eda42b8406
  languageName: node
  linkType: hard

"@react-aria/toolbar@npm:3.0.0-beta.15":
  version: 3.0.0-beta.15
  resolution: "@react-aria/toolbar@npm:3.0.0-beta.15"
  dependencies:
    "@react-aria/focus": "npm:^3.20.2"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/083bb96d45d0b82bb1a1188a28b0012281410e7c4f10f9421a08dbe01cd7f390a10cd53239ec4f938929a81d846f58e03db24e3fc95f4f7c4bff46d9d09629ce
  languageName: node
  linkType: hard

"@react-aria/tooltip@npm:^3.8.2":
  version: 3.8.2
  resolution: "@react-aria/tooltip@npm:3.8.2"
  dependencies:
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/tooltip": "npm:^3.5.3"
    "@react-types/shared": "npm:^3.29.0"
    "@react-types/tooltip": "npm:^3.4.16"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/673c7fcd0f7b051e2eddcb3b20c381124ad2ac103201f613b675b38c22163e704d0634fcbd0571e3351e25712d8cf15e9b844f2fedde18d5f5f1673101ff1951
  languageName: node
  linkType: hard

"@react-aria/tree@npm:^3.0.2":
  version: 3.0.2
  resolution: "@react-aria/tree@npm:3.0.2"
  dependencies:
    "@react-aria/gridlist": "npm:^3.12.0"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/selection": "npm:^3.24.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-stately/tree": "npm:^3.8.9"
    "@react-types/button": "npm:^3.12.0"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/67305d49981e488eae1ee84e37e67bf5089eff918ae9695a2e295ee2ccb203b617c2b73db5e3a5bb75d1dd29f9f3de05a7fa2b1dbe21aa70011c818610d20532
  languageName: node
  linkType: hard

"@react-aria/utils@npm:^3.28.2":
  version: 3.28.2
  resolution: "@react-aria/utils@npm:3.28.2"
  dependencies:
    "@react-aria/ssr": "npm:^3.9.8"
    "@react-stately/flags": "npm:^3.1.1"
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
    clsx: "npm:^2.0.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/7f8dbf6f37477ec34fd712d8db07f038ed3760ff9163591fef16e23a4fc373ab9458cdaae68bcd05ff1cf3a17e9ebc00910540b9df9540a2e97e626419766440
  languageName: node
  linkType: hard

"@react-aria/visually-hidden@npm:^3.8.22":
  version: 3.8.22
  resolution: "@react-aria/visually-hidden@npm:3.8.22"
  dependencies:
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/bde2da3d9e5df233942ca24f431d789474981c0587f8c5a649c4fc0e3611d521538f1d1344e7a7f6cad929be680ccd9b1142913c6c2c64944cb2786437301275
  languageName: node
  linkType: hard

"@react-stately/calendar@npm:^3.8.0":
  version: 3.8.0
  resolution: "@react-stately/calendar@npm:3.8.0"
  dependencies:
    "@internationalized/date": "npm:^3.8.0"
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/calendar": "npm:^3.7.0"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/9bec75f4e3c14a2d42b0691ad254143226a25d5713044cfe3cc3bfbc1fadd461d7b61851508a5b14e4664bec0ea4dd1e46bcd91cadb02da9edfc961c4606aada
  languageName: node
  linkType: hard

"@react-stately/checkbox@npm:^3.6.13":
  version: 3.6.13
  resolution: "@react-stately/checkbox@npm:3.6.13"
  dependencies:
    "@react-stately/form": "npm:^3.1.3"
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/checkbox": "npm:^3.9.3"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/faafe298eaec54c5752317a7ecf5f8958d428fad8d9488c602748735ad138f35c620f0c20315a20b7494d116546e85dc6e1015b4aa4f781da4107cb4f475f5c6
  languageName: node
  linkType: hard

"@react-stately/collections@npm:^3.12.3":
  version: 3.12.3
  resolution: "@react-stately/collections@npm:3.12.3"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/55c8fedca0b6b239e9dc595b076320db5a019ef2f3e9bfcb1ffc63904f83f9893007c3ecaec70e86f8ae27e186d6b2526604909b56b2787401694b52926f510d
  languageName: node
  linkType: hard

"@react-stately/color@npm:^3.8.4":
  version: 3.8.4
  resolution: "@react-stately/color@npm:3.8.4"
  dependencies:
    "@internationalized/number": "npm:^3.6.1"
    "@internationalized/string": "npm:^3.2.6"
    "@react-stately/form": "npm:^3.1.3"
    "@react-stately/numberfield": "npm:^3.9.11"
    "@react-stately/slider": "npm:^3.6.3"
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/color": "npm:^3.0.4"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/3ee385daba53a5afa0b4c93d8307696bc24e0227cecf935f231a5d0e0bc5ae20f6b547cce10f34555b21ba45b11ff41ac0d1d4d830c179fa602a32c3301272a5
  languageName: node
  linkType: hard

"@react-stately/combobox@npm:^3.10.4":
  version: 3.10.4
  resolution: "@react-stately/combobox@npm:3.10.4"
  dependencies:
    "@react-stately/collections": "npm:^3.12.3"
    "@react-stately/form": "npm:^3.1.3"
    "@react-stately/list": "npm:^3.12.1"
    "@react-stately/overlays": "npm:^3.6.15"
    "@react-stately/select": "npm:^3.6.12"
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/combobox": "npm:^3.13.4"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/e97e4a8eedcd85a51a47c56f7ef6d6f60b398d106d6e5b481a233feb2fa52c2d31e58b186c8e3687622af1dc112c9635687b2821af7c33068c1af7b861b25fa4
  languageName: node
  linkType: hard

"@react-stately/data@npm:^3.12.3":
  version: 3.12.3
  resolution: "@react-stately/data@npm:3.12.3"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/b45eefbcdb65f8232bdf6c0b9a55f6ca82803a26a2ae7a91bff80cded445114bd597d17f9648f3f0c05fa69552974945c57d4f6dea9e8d8b1c53ca605beeedd0
  languageName: node
  linkType: hard

"@react-stately/datepicker@npm:^3.14.0":
  version: 3.14.0
  resolution: "@react-stately/datepicker@npm:3.14.0"
  dependencies:
    "@internationalized/date": "npm:^3.8.0"
    "@internationalized/string": "npm:^3.2.6"
    "@react-stately/form": "npm:^3.1.3"
    "@react-stately/overlays": "npm:^3.6.15"
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/datepicker": "npm:^3.12.0"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/f1a8df6f0e70b656b03bdaa51e4ece02d4c237ad26279e3c70cc41986149e53121abe0a838ae0c95720568d5ebe056091457dab3452b2148ee62b9dfd407c692
  languageName: node
  linkType: hard

"@react-stately/disclosure@npm:^3.0.3":
  version: 3.0.3
  resolution: "@react-stately/disclosure@npm:3.0.3"
  dependencies:
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/bcd7308abbe5fb1cfa0d325ab7af598f53059d895f1c4f0930166f51c363a65caaef4fed4ef549e815f3414d22320dc55f4d2a3c54472122b71c1c7902701c05
  languageName: node
  linkType: hard

"@react-stately/dnd@npm:^3.5.3":
  version: 3.5.3
  resolution: "@react-stately/dnd@npm:3.5.3"
  dependencies:
    "@react-stately/selection": "npm:^3.20.1"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/470da7fc78c076523e0cc7c5f831492b68b1012cf81a82d8038e601d1fd08500eda9a307d7e16cf2b8ef79cf4008c56c7b0fecca336b21945058ed87aa47a9be
  languageName: node
  linkType: hard

"@react-stately/flags@npm:^3.1.1":
  version: 3.1.1
  resolution: "@react-stately/flags@npm:3.1.1"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  checksum: 10/1a0b8cd09d1c8ab6a9a1245338b093b4c3a5a5e854a096a41a33054dc0162accd91c6fa323cb215444f2d8949cdebba03e2ad590ea500f7234cf63c8c7c34e01
  languageName: node
  linkType: hard

"@react-stately/form@npm:^3.1.3":
  version: 3.1.3
  resolution: "@react-stately/form@npm:3.1.3"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/aa1ca95dd86a44cf0cb620ee6287190cd431aa79d8b144ff7c715bc27e9396462cf43e30221149e5b30a5d66df1cc03f77d466c6edd164dab160952ae7952cfc
  languageName: node
  linkType: hard

"@react-stately/grid@npm:^3.11.1":
  version: 3.11.1
  resolution: "@react-stately/grid@npm:3.11.1"
  dependencies:
    "@react-stately/collections": "npm:^3.12.3"
    "@react-stately/selection": "npm:^3.20.1"
    "@react-types/grid": "npm:^3.3.1"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/a9725b328723d3a144e32832772d4aed3c0218f9831addd7a1e7cb288df7b6008b01823bbe6b54a22c9a24d5c2d0c2a0144156ae2daa3681010a9c66c5b06fc3
  languageName: node
  linkType: hard

"@react-stately/list@npm:^3.12.1":
  version: 3.12.1
  resolution: "@react-stately/list@npm:3.12.1"
  dependencies:
    "@react-stately/collections": "npm:^3.12.3"
    "@react-stately/selection": "npm:^3.20.1"
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/00ee82ad0b8e60499bd51f02c3e86bc5cdfe0aacdc7b35f4135322f5400aeeddb447cbcac723b0f41f13dae88952346bee4afcf878a9b1415b1cd49e81016afd
  languageName: node
  linkType: hard

"@react-stately/menu@npm:^3.9.3":
  version: 3.9.3
  resolution: "@react-stately/menu@npm:3.9.3"
  dependencies:
    "@react-stately/overlays": "npm:^3.6.15"
    "@react-types/menu": "npm:^3.10.0"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/858f6cc2f5a67c0d80274670f7d80d93b7fcb88dc538caae72dde5c3339530304e76f38f7d708d3eb710668ef9696a00446d1df7f2a9ebf6ef6ea0cc7db1e7cd
  languageName: node
  linkType: hard

"@react-stately/numberfield@npm:^3.9.11":
  version: 3.9.11
  resolution: "@react-stately/numberfield@npm:3.9.11"
  dependencies:
    "@internationalized/number": "npm:^3.6.1"
    "@react-stately/form": "npm:^3.1.3"
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/numberfield": "npm:^3.8.10"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/e8226476ff5be2374245498fbc89f4db7e3160d6e768275b640fcbdcee0fa3bb99b6607dfeb593e2b0e48895fe1efb199028f2fa14815ae8ac519c26dc0dc1a3
  languageName: node
  linkType: hard

"@react-stately/overlays@npm:^3.6.15":
  version: 3.6.15
  resolution: "@react-stately/overlays@npm:3.6.15"
  dependencies:
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/overlays": "npm:^3.8.14"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/7fcb380fa7a0768089c5afd579b839f11702806efb3b2c818878909a8c88e9db2de9a87d8ec84cdc780f03c0639884b65b9b9b27dc7ab2481aeee27919878130
  languageName: node
  linkType: hard

"@react-stately/radio@npm:^3.10.12":
  version: 3.10.12
  resolution: "@react-stately/radio@npm:3.10.12"
  dependencies:
    "@react-stately/form": "npm:^3.1.3"
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/radio": "npm:^3.8.8"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/7bd2bf0fa6cc95f6b25b45be56d4639339c5299d97f1b44a20a493e8bb7c1b7b0fbbb014ff6d72e8726adf4941b4793077fba8435bb5668478504032539b221f
  languageName: node
  linkType: hard

"@react-stately/searchfield@npm:^3.5.11":
  version: 3.5.11
  resolution: "@react-stately/searchfield@npm:3.5.11"
  dependencies:
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/searchfield": "npm:^3.6.1"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/9e5c9d6381b896dfdc155041680ee710a270413f431961a06cf89fc338adcc72c7f7c817a2e0a5ff3f4df7fcc4c4cbdec0cca85d2a012fdb2722c927e6b902e2
  languageName: node
  linkType: hard

"@react-stately/select@npm:^3.6.12":
  version: 3.6.12
  resolution: "@react-stately/select@npm:3.6.12"
  dependencies:
    "@react-stately/form": "npm:^3.1.3"
    "@react-stately/list": "npm:^3.12.1"
    "@react-stately/overlays": "npm:^3.6.15"
    "@react-types/select": "npm:^3.9.11"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/843b42cb3623f8c13f56a9212b996d02afc199af68bfadb48446dd73c3d3cc8cf9955820ae1d316cb979570978cb7f9d3e5010c9213b9e67c7c34218a5097103
  languageName: node
  linkType: hard

"@react-stately/selection@npm:^3.20.1":
  version: 3.20.1
  resolution: "@react-stately/selection@npm:3.20.1"
  dependencies:
    "@react-stately/collections": "npm:^3.12.3"
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/7747d001a05c5a56d87870563aed556c8ee7e9a7e96b5acc43597b3192df20b861ca7fb208d99cd95efc1a515e435d761fbce5693a933ca1b93f446595f99895
  languageName: node
  linkType: hard

"@react-stately/slider@npm:^3.6.3":
  version: 3.6.3
  resolution: "@react-stately/slider@npm:3.6.3"
  dependencies:
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/shared": "npm:^3.29.0"
    "@react-types/slider": "npm:^3.7.10"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/dade693e23f70a110aa77b22c90cda4fa04fb0ae6b1fc4be0cb19ea213f4bfc2c0b8ee745de24e9fc5d9c877ffb34cfb537e8d9243ee23630ac5c3b382fb769e
  languageName: node
  linkType: hard

"@react-stately/table@npm:^3.14.1":
  version: 3.14.1
  resolution: "@react-stately/table@npm:3.14.1"
  dependencies:
    "@react-stately/collections": "npm:^3.12.3"
    "@react-stately/flags": "npm:^3.1.1"
    "@react-stately/grid": "npm:^3.11.1"
    "@react-stately/selection": "npm:^3.20.1"
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/grid": "npm:^3.3.1"
    "@react-types/shared": "npm:^3.29.0"
    "@react-types/table": "npm:^3.12.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/9e07f1d54c97fcf7b0c783060af6db01598367013d5f8bbe77eec15c5f14111d44728c5b2efb4f51e55c2c28b8f9fa12063beccb9db37f3c7ce9f07b1eaa203e
  languageName: node
  linkType: hard

"@react-stately/tabs@npm:^3.8.1":
  version: 3.8.1
  resolution: "@react-stately/tabs@npm:3.8.1"
  dependencies:
    "@react-stately/list": "npm:^3.12.1"
    "@react-types/shared": "npm:^3.29.0"
    "@react-types/tabs": "npm:^3.3.14"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/5be66038e34d5ac636d75b5ec1d022fd4ce88f847e01c6013c40444752b728c34697fdcfe99600fc94a7a57146ef22c5f1560dff61b6f750a78beb4c5224b392
  languageName: node
  linkType: hard

"@react-stately/toast@npm:^3.1.0":
  version: 3.1.0
  resolution: "@react-stately/toast@npm:3.1.0"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
    use-sync-external-store: "npm:^1.4.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/54e222e068433791549ff5f5238690384e2246041f41fdcae2d0e9d629569c3073d2aa92751601c0f1526a3cd0ca2cd2918c7031a753b8c897df99ce7280ab95
  languageName: node
  linkType: hard

"@react-stately/toggle@npm:^3.8.3":
  version: 3.8.3
  resolution: "@react-stately/toggle@npm:3.8.3"
  dependencies:
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/checkbox": "npm:^3.9.3"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/f60a53fa3adc10f28894f0dd8983bf2047f0eccfa9912c6494036f0f1e30de0a92f54b936eaac9fb6afa12bb8b71aa738dfe265c9ed04d125f3412b15fca4076
  languageName: node
  linkType: hard

"@react-stately/tooltip@npm:^3.5.3":
  version: 3.5.3
  resolution: "@react-stately/tooltip@npm:3.5.3"
  dependencies:
    "@react-stately/overlays": "npm:^3.6.15"
    "@react-types/tooltip": "npm:^3.4.16"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/30dbf2a1f2153d04a3726d6c8cd32e865028c43aad6f6c78acdc57a51152e2996dff50c199a3cdae776d083173bbcf3e820782816b677b178b9b9ac06b84e1f9
  languageName: node
  linkType: hard

"@react-stately/tree@npm:^3.8.9":
  version: 3.8.9
  resolution: "@react-stately/tree@npm:3.8.9"
  dependencies:
    "@react-stately/collections": "npm:^3.12.3"
    "@react-stately/selection": "npm:^3.20.1"
    "@react-stately/utils": "npm:^3.10.6"
    "@react-types/shared": "npm:^3.29.0"
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/659eb69573bd8695180a1215157bd230631d9f3f36f8928b09c2075dc4839a28c1cbd5e70cda9db7453c2793a3c7f82560e06f5090a50424c83a41a38f104769
  languageName: node
  linkType: hard

"@react-stately/utils@npm:^3.10.6":
  version: 3.10.6
  resolution: "@react-stately/utils@npm:3.10.6"
  dependencies:
    "@swc/helpers": "npm:^0.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/84a2374b2dbc343af0c88283b331ddd5a8916edc0a5413189f572da77f37860d0a971cf7dc0b401fcc0be8c972af85099c11c704ea59bcbb8e57347832deee27
  languageName: node
  linkType: hard

"@react-types/breadcrumbs@npm:^3.7.12":
  version: 3.7.12
  resolution: "@react-types/breadcrumbs@npm:3.7.12"
  dependencies:
    "@react-types/link": "npm:^3.6.0"
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/19edb0b7533e5bc68b3e17e19022c3b4171905bb575f605b48dc366c54d92231cb038ddb5c6662e6a0353046228f1b737a6386a77efbc7b4d18774a338684dc2
  languageName: node
  linkType: hard

"@react-types/button@npm:^3.12.0":
  version: 3.12.0
  resolution: "@react-types/button@npm:3.12.0"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/a82cc70804bfdd72422ec359be174ed3ccac545d96e9a07a21b3080166f0045080bef4b819d2f6c6c540e5008c7cd5dfa85c881215b6dd178a2b605129e0fb1b
  languageName: node
  linkType: hard

"@react-types/calendar@npm:^3.7.0":
  version: 3.7.0
  resolution: "@react-types/calendar@npm:3.7.0"
  dependencies:
    "@internationalized/date": "npm:^3.8.0"
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/fb58ff9bca419d30e7149c902da63818d60aa36a1858bbc689458af6ee57445859c5ecdfd117d312b173104e598ec318e4399571a7d68f4fb011c54c9d220599
  languageName: node
  linkType: hard

"@react-types/checkbox@npm:^3.9.3":
  version: 3.9.3
  resolution: "@react-types/checkbox@npm:3.9.3"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/423d36836386653ed8f26a7c114213fc388450d899e0a707f9328a6a533be2e7ffb19b24288ed3e5be9fe747afbad40ddd187a8f855fd776b70918a5d7c0fe8f
  languageName: node
  linkType: hard

"@react-types/color@npm:^3.0.4":
  version: 3.0.4
  resolution: "@react-types/color@npm:3.0.4"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
    "@react-types/slider": "npm:^3.7.10"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/ad62956ba04a1c9a3ea4d79a5f72e6f1e34a76d35566fa1b070c9ffdd1ec2f9bd539143fc3ed40bce48737be4392d86f55ad07c37550a2dfa3f1e53bcde5a3a8
  languageName: node
  linkType: hard

"@react-types/combobox@npm:^3.13.4":
  version: 3.13.4
  resolution: "@react-types/combobox@npm:3.13.4"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/e07c3a3ff1b5d3619d74d5a3d63f7a50c2437691e88db79544adb8ee8fb99e923bd264b3bca097451fbbaca947bac24cdbc3fa8f4da82ded027c933f6b4acc6c
  languageName: node
  linkType: hard

"@react-types/datepicker@npm:^3.12.0":
  version: 3.12.0
  resolution: "@react-types/datepicker@npm:3.12.0"
  dependencies:
    "@internationalized/date": "npm:^3.8.0"
    "@react-types/calendar": "npm:^3.7.0"
    "@react-types/overlays": "npm:^3.8.14"
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/dbad55ec4c68ab00e4e9541938fcf57daa00510b872d28a54c0cc1c1788469b6b33c16cce825f7c299980c3ed4f29479f3103e32973bd7ce3338833be66616ee
  languageName: node
  linkType: hard

"@react-types/dialog@npm:^3.5.17":
  version: 3.5.17
  resolution: "@react-types/dialog@npm:3.5.17"
  dependencies:
    "@react-types/overlays": "npm:^3.8.14"
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/079391fd1175540ae88be75cdb01e4a3b8fb490313fd0eb1c695346fd70e732143dcdc1229cf783a01906b4d0e8176d539a6873802698fde3dda27e481e1e038
  languageName: node
  linkType: hard

"@react-types/grid@npm:^3.3.1":
  version: 3.3.1
  resolution: "@react-types/grid@npm:3.3.1"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/8d56d1799573c827c2b1d90caedb60462f7bdba9d9873bd3713db3ccebb997a83f792a36bfa0441a411c8ebe6943d750395cd55388dba6a58a6d3a36f94d5ac4
  languageName: node
  linkType: hard

"@react-types/link@npm:^3.6.0":
  version: 3.6.0
  resolution: "@react-types/link@npm:3.6.0"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/7e72f22a8fbc9b5c4595e3559a6798333fa1f01e91033964304d5cfad5596832e1744cda0f0a709b128f0bdb8e2e3ec62e5becd247eba150c26601c995a00b7b
  languageName: node
  linkType: hard

"@react-types/listbox@npm:^3.6.0":
  version: 3.6.0
  resolution: "@react-types/listbox@npm:3.6.0"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/16f59d5dc9ec9b1129e7ab8c272f25851cc5bda802b8da9f353c6aa6b3581a8fabeb1c8631ca017406ad34187b65eb664cf946de5de705cb25d9041f91490dc1
  languageName: node
  linkType: hard

"@react-types/menu@npm:^3.10.0":
  version: 3.10.0
  resolution: "@react-types/menu@npm:3.10.0"
  dependencies:
    "@react-types/overlays": "npm:^3.8.14"
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/866b82273cbb8247989cd337c24ba3b03c85257e6f1b7ecdc06d985e7bf92667c08d5f8ac2cbbe42a9d8855b4c81c83cada5317449fe4d47360d76ccc07c39c0
  languageName: node
  linkType: hard

"@react-types/meter@npm:^3.4.8":
  version: 3.4.8
  resolution: "@react-types/meter@npm:3.4.8"
  dependencies:
    "@react-types/progress": "npm:^3.5.11"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/105aafe1da2557a739691c5d93eb95402b91dbe85736f5453a98901545fdcb15fe9d7ebd2d8a3b323e1e1e4e9bcb24d4f8c3bb0b48542d62573fd340f6a91339
  languageName: node
  linkType: hard

"@react-types/numberfield@npm:^3.8.10":
  version: 3.8.10
  resolution: "@react-types/numberfield@npm:3.8.10"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/81bd0286308b6ab6ed80c64267631bae86b98ab0fe282cf448a46f891d39bf236e0d946b366716f12991706b2175453fa97920ee9e9b88d2fabb46a2e1be4872
  languageName: node
  linkType: hard

"@react-types/overlays@npm:^3.8.14":
  version: 3.8.14
  resolution: "@react-types/overlays@npm:3.8.14"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/fa22ba3403708c03f73eda0614937602b0a2b89bb07d08732e5429c67621afbf4bed32c8ad1ed1b5ddc92679077be69c6058fd409050cfc21d7173431a68749d
  languageName: node
  linkType: hard

"@react-types/progress@npm:^3.5.11":
  version: 3.5.11
  resolution: "@react-types/progress@npm:3.5.11"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/9919190a5594b3134048e0875a568aa7391dc51e23ca3a336b10eabfe91d830ebe7847be1c3813d541ee3ed5fda071bde9f6b9062b26d5b6895a81d46615b46f
  languageName: node
  linkType: hard

"@react-types/radio@npm:^3.8.8":
  version: 3.8.8
  resolution: "@react-types/radio@npm:3.8.8"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/54557c396c14d361d31b0d51a3110571164b763c0fdcbafb8b56a2faaf175c2f082285614d6ebc20359e750e22ed4ed32aa36104611652e1753f9505a2e5a7be
  languageName: node
  linkType: hard

"@react-types/searchfield@npm:^3.6.1":
  version: 3.6.1
  resolution: "@react-types/searchfield@npm:3.6.1"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
    "@react-types/textfield": "npm:^3.12.1"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/ed2e158d90fc7de1365da2db3e5dfd141c92350d568e342aae1da5f019462c593ae3751f90786a5eb25151ab898b0b305e7d7233b2bcd4417c32f201f0386f1c
  languageName: node
  linkType: hard

"@react-types/select@npm:^3.9.11":
  version: 3.9.11
  resolution: "@react-types/select@npm:3.9.11"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/1c98453a86ab287d727fde64d44df1cff1d8da3d5edb5744f047f44c42d1618d2ef26a5dfa44136a0d0532eb5b63e61cb393c2c6951b3f9683166492748737e4
  languageName: node
  linkType: hard

"@react-types/shared@npm:^3.29.0":
  version: 3.29.0
  resolution: "@react-types/shared@npm:3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/29bbe50366f9a0991769aa40311bc79b20cf6df228ac379599756c6483f3926a01f99b60ff9c86537c610bb9261c99a93f52678d71ff6614964fccf53d469c5e
  languageName: node
  linkType: hard

"@react-types/slider@npm:^3.7.10":
  version: 3.7.10
  resolution: "@react-types/slider@npm:3.7.10"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/4f9286e9c05e370e9b43c6cb1de4afef1f7bc494e2587060d535a1ad41226a200360670a6643d02f678a2288da45d2733bfbc1a26301a2181fb6dbb361692cf1
  languageName: node
  linkType: hard

"@react-types/switch@npm:^3.5.10":
  version: 3.5.10
  resolution: "@react-types/switch@npm:3.5.10"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/dc7a7ce5be15ed97ed78ff05b185fa2c658b691c42693529cc664da8ff90ab782c9ad9428b3b7ffee9e35a504772373de793b0d588d83cbeec727a7aa24dc113
  languageName: node
  linkType: hard

"@react-types/table@npm:^3.12.0":
  version: 3.12.0
  resolution: "@react-types/table@npm:3.12.0"
  dependencies:
    "@react-types/grid": "npm:^3.3.1"
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/8760e65c8c5485cf97c3a67f5896cf9907d696f87db3bbeb26ea299c6b7211cd8799050989c12b8d23101f24a35020bccd091df43faa032329bd01323e268bf6
  languageName: node
  linkType: hard

"@react-types/tabs@npm:^3.3.14":
  version: 3.3.14
  resolution: "@react-types/tabs@npm:3.3.14"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/db574da4820a558c3ce4d945bd467c0a7c4777f6d10f604e5771622c7a55d0251047fd138f843380eb7e86b979d806eec9b9a60dc01eb27f2b02d1d0d74dc093
  languageName: node
  linkType: hard

"@react-types/textfield@npm:^3.12.1":
  version: 3.12.1
  resolution: "@react-types/textfield@npm:3.12.1"
  dependencies:
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/0889424c89d749048e98a913b60715c012263a0100a8c71f3dd05a50abc9ee66126e6548a955d0fd4d2a7fad5a1523d38aaf60407a4a562f4237dd458f48ad57
  languageName: node
  linkType: hard

"@react-types/tooltip@npm:^3.4.16":
  version: 3.4.16
  resolution: "@react-types/tooltip@npm:3.4.16"
  dependencies:
    "@react-types/overlays": "npm:^3.8.14"
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/c7fd866196e12937b9653ce9422af7efd4ace851751bea9a94023af916ceed59dddd4812604f72080cc47248084b1373e4092e9b7d9bc1180817f256611d02ca
  languageName: node
  linkType: hard

"@rtsao/scc@npm:^1.1.0":
  version: 1.1.0
  resolution: "@rtsao/scc@npm:1.1.0"
  checksum: 10/17d04adf404e04c1e61391ed97bca5117d4c2767a76ae3e879390d6dec7b317fcae68afbf9e98badee075d0b64fa60f287729c4942021b4d19cd01db77385c01
  languageName: node
  linkType: hard

"@rushstack/eslint-patch@npm:^1.10.3":
  version: 1.11.0
  resolution: "@rushstack/eslint-patch@npm:1.11.0"
  checksum: 10/9bb3eb4a48a9a55e31d302b8b99f405e0f3e436fc3cda8c869fdd3fefd3ac398f5a353cceaa6c8cc5e5baf03ccd88d7965fbd25eb111f1f334415f95fa0f996d
  languageName: node
  linkType: hard

"@stripe/react-stripe-js@npm:^1.7.2":
  version: 1.16.5
  resolution: "@stripe/react-stripe-js@npm:1.16.5"
  dependencies:
    prop-types: "npm:^15.7.2"
  peerDependencies:
    "@stripe/stripe-js": ^1.44.1
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: 10/b6413e1122fac91834919cea2683c31a9ee2525ce90b78b86b0337e8f506aeff5049fb1fd097019b34e70d4bc6657b98ae40c352e433d01ec47d8ceae459dd15
  languageName: node
  linkType: hard

"@stripe/stripe-js@npm:^1.29.0":
  version: 1.54.2
  resolution: "@stripe/stripe-js@npm:1.54.2"
  checksum: 10/52d6e814dd6e4979a050d9c06d8aa463f583138bb6b1aaf6e8856e92dcfa3ce7fd2231ce86882b44ffd0299b8e1d93f6c6dd9d1461b957ef2a6d548a801e506f
  languageName: node
  linkType: hard

"@swc/counter@npm:0.1.3":
  version: 0.1.3
  resolution: "@swc/counter@npm:0.1.3"
  checksum: 10/df8f9cfba9904d3d60f511664c70d23bb323b3a0803ec9890f60133954173047ba9bdeabce28cd70ba89ccd3fd6c71c7b0bd58be85f611e1ffbe5d5c18616598
  languageName: node
  linkType: hard

"@swc/helpers@npm:0.5.15":
  version: 0.5.15
  resolution: "@swc/helpers@npm:0.5.15"
  dependencies:
    tslib: "npm:^2.8.0"
  checksum: 10/e3f32c6deeecfb0fa3f22edff03a7b358e7ce16d27b0f1c8b5cdc3042c5c4ce4da6eac0b781ab7cc4f54696ece657467d56734fb26883439fb00017385364c4c
  languageName: node
  linkType: hard

"@swc/helpers@npm:^0.5.0":
  version: 0.5.17
  resolution: "@swc/helpers@npm:0.5.17"
  dependencies:
    tslib: "npm:^2.8.0"
  checksum: 10/1fc8312a78f1f99c8ec838585445e99763eeebff2356100738cdfdb8ad47d2d38df678ee6edd93a90fe319ac52da67adc14ac00eb82b606c5fb8ebc5d06ec2a2
  languageName: node
  linkType: hard

"@tailwindcss/forms@npm:^0.5.3":
  version: 0.5.10
  resolution: "@tailwindcss/forms@npm:0.5.10"
  dependencies:
    mini-svg-data-uri: "npm:^1.2.3"
  peerDependencies:
    tailwindcss: ">=3.0.0 || >= 3.0.0-alpha.1 || >= 4.0.0-alpha.20 || >= 4.0.0-beta.1"
  checksum: 10/d67ea58d8e92a262455bafd1b88772f5d9dbdc034f70d37b31af3617d1505231ff485c1209467715d139f392cd2feb43e3cdb4656816594e97c1304054e121d6
  languageName: node
  linkType: hard

"@tanstack/react-table@npm:8.20.5":
  version: 8.20.5
  resolution: "@tanstack/react-table@npm:8.20.5"
  dependencies:
    "@tanstack/table-core": "npm:8.20.5"
  peerDependencies:
    react: ">=16.8"
    react-dom: ">=16.8"
  checksum: 10/df67094795a0b7e4b34f73abe346443c2e806c572fea31b58759aa8ec5274f613e5e6941090eb16f861bda10d3088731bc6e7f15e5f90326db273bc55b9141ce
  languageName: node
  linkType: hard

"@tanstack/react-virtual@npm:^3.13.6":
  version: 3.13.8
  resolution: "@tanstack/react-virtual@npm:3.13.8"
  dependencies:
    "@tanstack/virtual-core": "npm:3.13.8"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10/a68db4cccc0de54ba0da2ae4292671bd4fa339d1cdfab160d8a067d5bb78f77e2f75d234089d4cf45bc688e51f702604c7f2d4031f8e9b8ce82cb71a0fa7b4f0
  languageName: node
  linkType: hard

"@tanstack/table-core@npm:8.20.5":
  version: 8.20.5
  resolution: "@tanstack/table-core@npm:8.20.5"
  checksum: 10/5408237920d5796951e925278edbbe76f71006627a4e3da248a810970256f75d973538fe7ae75a32155d4a25a95abc4fffaea337b5120f7940d7e664dc9da87f
  languageName: node
  linkType: hard

"@tanstack/virtual-core@npm:3.13.8":
  version: 3.13.8
  resolution: "@tanstack/virtual-core@npm:3.13.8"
  checksum: 10/21982fa3b55bb4a7936a013b5ec151ce636eb108b38b56519391b9ebffc1cbcd81de0472229d237f84fc8c44e61a6a3890dba6371d04d921a61023d506902a0b
  languageName: node
  linkType: hard

"@tybys/wasm-util@npm:^0.9.0":
  version: 0.9.0
  resolution: "@tybys/wasm-util@npm:0.9.0"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10/aa58e64753a420ad1eefaf7bacef3dda61d74f9336925943d9244132d5b48d9242f734f1e707fd5ccfa6dd1d8ec8e6debc234b4dedb3a5b0d8486d1f373350b2
  languageName: node
  linkType: hard

"@types/debug@npm:^4.0.0":
  version: 4.1.12
  resolution: "@types/debug@npm:4.1.12"
  dependencies:
    "@types/ms": "npm:*"
  checksum: 10/47876a852de8240bfdaf7481357af2b88cb660d30c72e73789abf00c499d6bc7cd5e52f41c915d1b9cd8ec9fef5b05688d7b7aef17f7f272c2d04679508d1053
  languageName: node
  linkType: hard

"@types/eslint-scope@npm:^3.7.7":
  version: 3.7.7
  resolution: "@types/eslint-scope@npm:3.7.7"
  dependencies:
    "@types/eslint": "npm:*"
    "@types/estree": "npm:*"
  checksum: 10/e2889a124aaab0b89af1bab5959847c5bec09809209255de0e63b9f54c629a94781daa04adb66bffcdd742f5e25a17614fb933965093c0eea64aacda4309380e
  languageName: node
  linkType: hard

"@types/eslint@npm:*":
  version: 9.6.1
  resolution: "@types/eslint@npm:9.6.1"
  dependencies:
    "@types/estree": "npm:*"
    "@types/json-schema": "npm:*"
  checksum: 10/719fcd255760168a43d0e306ef87548e1e15bffe361d5f4022b0f266575637acc0ecb85604ac97879ee8ae83c6a6d0613b0ed31d0209ddf22a0fe6d608fc56fe
  languageName: node
  linkType: hard

"@types/estree-jsx@npm:^1.0.0":
  version: 1.0.5
  resolution: "@types/estree-jsx@npm:1.0.5"
  dependencies:
    "@types/estree": "npm:*"
  checksum: 10/a028ab0cd7b2950168a05c6a86026eb3a36a54a4adfae57f13911d7b49dffe573d9c2b28421b2d029b49b3d02fcd686611be2622dc3dad6d9791166c083f6008
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:^1.0.0, @types/estree@npm:^1.0.6":
  version: 1.0.7
  resolution: "@types/estree@npm:1.0.7"
  checksum: 10/419c845ece767ad4b21171e6e5b63dabb2eb46b9c0d97361edcd9cabbf6a95fcadb91d89b5fa098d1336fa0b8fceaea82fca97a2ef3971f5c86e53031e157b21
  languageName: node
  linkType: hard

"@types/hast@npm:^3.0.0":
  version: 3.0.4
  resolution: "@types/hast@npm:3.0.4"
  dependencies:
    "@types/unist": "npm:*"
  checksum: 10/732920d81bb7605895776841b7658b4d8cc74a43a8fa176017cc0fb0ecc1a4c82a2b75a4fe6b71aa262b649d3fb62858c6789efa3793ea1d40269953af96ecb5
  languageName: node
  linkType: hard

"@types/json-schema@npm:*, @types/json-schema@npm:^7.0.15, @types/json-schema@npm:^7.0.5, @types/json-schema@npm:^7.0.9":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10/1a3c3e06236e4c4aab89499c428d585527ce50c24fe8259e8b3926d3df4cfbbbcf306cfc73ddfb66cbafc973116efd15967020b0f738f63e09e64c7d260519e7
  languageName: node
  linkType: hard

"@types/json5@npm:^0.0.29":
  version: 0.0.29
  resolution: "@types/json5@npm:0.0.29"
  checksum: 10/4e5aed58cabb2bbf6f725da13421aa50a49abb6bc17bfab6c31b8774b073fa7b50d557c61f961a09a85f6056151190f8ac95f13f5b48136ba5841f7d4484ec56
  languageName: node
  linkType: hard

"@types/lodash@npm:^4.14.195":
  version: 4.17.16
  resolution: "@types/lodash@npm:4.17.16"
  checksum: 10/9a8bb7471a7521bd65d528e1bd14f79819a3eeb6f8a35a8a44649a7d773775c0813e93fd93bd32ccf350bb076c0bf02c6d47877c4625f526f6dd4d283c746aec
  languageName: node
  linkType: hard

"@types/mdast@npm:^4.0.0":
  version: 4.0.4
  resolution: "@types/mdast@npm:4.0.4"
  dependencies:
    "@types/unist": "npm:*"
  checksum: 10/efe3ec11b9ee0015a396c4fb4cd1b6f31b51b8ae9783c59560e6fc0bf6c2fa1dcc7fccaf45fa09a6c8b3397fab9dc8d431433935cae3835caa70a18f7fc775f8
  languageName: node
  linkType: hard

"@types/ms@npm:*":
  version: 2.1.0
  resolution: "@types/ms@npm:2.1.0"
  checksum: 10/532d2ebb91937ccc4a89389715e5b47d4c66e708d15942fe6cc25add6dc37b2be058230a327dd50f43f89b8b6d5d52b74685a9e8f70516edfc9bdd6be910eff4
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 22.15.14
  resolution: "@types/node@npm:22.15.14"
  dependencies:
    undici-types: "npm:~6.21.0"
  checksum: 10/94d24bb024a770d5ca7c4462f5c6186dee3d3429513d28f0ee7af1cf0f9adc938b0410183bb638c2ee99609384618e53e4c0fa2089915c8b31f514553a02d242
  languageName: node
  linkType: hard

"@types/node@npm:17.0.21":
  version: 17.0.21
  resolution: "@types/node@npm:17.0.21"
  checksum: 10/2beae12b0240834801d45d1f6afec1905325054ba0768ba8fa60144eea62ac3751cb2b787a32795c1611a870b2cb4bf4d58caf21f20b126b2bb2454fe6e2437c
  languageName: node
  linkType: hard

"@types/pg@npm:^8.11.0":
  version: 8.15.0
  resolution: "@types/pg@npm:8.15.0"
  dependencies:
    "@types/node": "npm:*"
    pg-protocol: "npm:*"
    pg-types: "npm:^4.0.1"
  checksum: 10/c7b4bbf2a2b4385d4acd25403577c3d359e6e30790a34cf9316149c1b32b041e2dfd9a039205e910be3a08d5278e2ca3d94a2fafade217091dd0b76744285750
  languageName: node
  linkType: hard

"@types/prismjs@npm:^1.26.0":
  version: 1.26.5
  resolution: "@types/prismjs@npm:1.26.5"
  checksum: 10/617099479db9550119d0f84272dc79d64b2cf3e0d7a17167fe740d55fdf0f155697d935409464392d164e62080c2c88d649cf4bc4fdd30a87127337536657277
  languageName: node
  linkType: hard

"@types/react-dom@npm:types-react-dom@19.0.0-rc.1":
  version: 19.0.0-rc.1
  resolution: "types-react-dom@npm:19.0.0-rc.1"
  dependencies:
    "@types/react": "npm:*"
  checksum: 10/fca4e7565308a109d4b126e5c7f5c387789a0187c9a46cb2a5af6120a7014beaa05736841be0ae658c7115871088500ddb0ef0bce60a21af4ec899cee8bb3c75
  languageName: node
  linkType: hard

"@types/react@npm:types-react@19.0.0-rc.1":
  version: 19.0.0-rc.1
  resolution: "types-react@npm:19.0.0-rc.1"
  dependencies:
    csstype: "npm:^3.0.2"
  checksum: 10/342da9ffeab93600a0cff4c8829e5350d935577e3f81bce7ead41d7cd074035e2c4a4bdd976fa8e3f5390fe6a32169370a805291a88a77c2f2ce2613bde54587
  languageName: node
  linkType: hard

"@types/unist@npm:*, @types/unist@npm:^3.0.0":
  version: 3.0.3
  resolution: "@types/unist@npm:3.0.3"
  checksum: 10/96e6453da9e075aaef1dc22482463898198acdc1eeb99b465e65e34303e2ec1e3b1ed4469a9118275ec284dc98019f63c3f5d49422f0e4ac707e5ab90fb3b71a
  languageName: node
  linkType: hard

"@types/unist@npm:^2.0.0":
  version: 2.0.11
  resolution: "@types/unist@npm:2.0.11"
  checksum: 10/6d436e832bc35c6dde9f056ac515ebf2b3384a1d7f63679d12358766f9b313368077402e9c1126a14d827f10370a5485e628bf61aa91117cf4fc882423191a4e
  languageName: node
  linkType: hard

"@types/wicg-file-system-access@npm:^2023.10.5":
  version: 2023.10.6
  resolution: "@types/wicg-file-system-access@npm:2023.10.6"
  checksum: 10/500ea4874dad6902b5a529b53b65b73f72c75b93b5b1593176d3e3679a35bcd17120a863a4eb5ec1e33ad583708eff7b2e6a92cb42b0bd66242281b322fb03be
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0":
  version: 8.32.0
  resolution: "@typescript-eslint/eslint-plugin@npm:8.32.0"
  dependencies:
    "@eslint-community/regexpp": "npm:^4.10.0"
    "@typescript-eslint/scope-manager": "npm:8.32.0"
    "@typescript-eslint/type-utils": "npm:8.32.0"
    "@typescript-eslint/utils": "npm:8.32.0"
    "@typescript-eslint/visitor-keys": "npm:8.32.0"
    graphemer: "npm:^1.4.0"
    ignore: "npm:^5.3.1"
    natural-compare: "npm:^1.4.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    "@typescript-eslint/parser": ^8.0.0 || ^8.0.0-alpha.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10/358b337948b2037816c3692c4ebfdb2eff90d367c6f3cdc5615c51be4eebc668c1c44e5fdfc71c08625f08b8f714ce6d0e59eccc7fe6cdabdd0800eb8ea3ab81
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0":
  version: 8.32.0
  resolution: "@typescript-eslint/parser@npm:8.32.0"
  dependencies:
    "@typescript-eslint/scope-manager": "npm:8.32.0"
    "@typescript-eslint/types": "npm:8.32.0"
    "@typescript-eslint/typescript-estree": "npm:8.32.0"
    "@typescript-eslint/visitor-keys": "npm:8.32.0"
    debug: "npm:^4.3.4"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10/05a9c0772a20085dc9def0a44d72421fad08b73eeb3bff474397ef719abb282ff684c59875e5cde3ad853ea6cff69b33312b9731f78b85de45b12a8158c97c2e
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.32.0":
  version: 8.32.0
  resolution: "@typescript-eslint/scope-manager@npm:8.32.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.32.0"
    "@typescript-eslint/visitor-keys": "npm:8.32.0"
  checksum: 10/44fb2b4b22cb30c5602db8861f3037479d98c9e812a0e5d7dfda349351c747aaf84be5c2a15b325e0c8eabf56faf2d0b66796b86a30a60a6c1f551bcce7cc05a
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.32.0":
  version: 8.32.0
  resolution: "@typescript-eslint/type-utils@npm:8.32.0"
  dependencies:
    "@typescript-eslint/typescript-estree": "npm:8.32.0"
    "@typescript-eslint/utils": "npm:8.32.0"
    debug: "npm:^4.3.4"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10/cb2a2bc3748a00f4f43e8262e2a929cac866ffe887493ae5fc5e935915c3b65f6cc7627754f6d16a47ff70a4306a54b9141fa7797518f17ed92421272aa24c45
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.32.0":
  version: 8.32.0
  resolution: "@typescript-eslint/types@npm:8.32.0"
  checksum: 10/52514975451f562206f0dcc90484ba8e2ddff9dde479b77f6ecbdf50cd5269e30f6c2bf80091204d2223d818268dd96fa396cb73434364754e730d286d6684ac
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.32.0":
  version: 8.32.0
  resolution: "@typescript-eslint/typescript-estree@npm:8.32.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.32.0"
    "@typescript-eslint/visitor-keys": "npm:8.32.0"
    debug: "npm:^4.3.4"
    fast-glob: "npm:^3.3.2"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^9.0.4"
    semver: "npm:^7.6.0"
    ts-api-utils: "npm:^2.1.0"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10/bb86ef5d3d5f4d1542d175ffb9662b8f9bffa17445646d40bfaad494627f2f10cd37f747403a283786f034e6174a1dfe01d9d7645c1f605d820fad7292541c7f
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.32.0":
  version: 8.32.0
  resolution: "@typescript-eslint/utils@npm:8.32.0"
  dependencies:
    "@eslint-community/eslint-utils": "npm:^4.7.0"
    "@typescript-eslint/scope-manager": "npm:8.32.0"
    "@typescript-eslint/types": "npm:8.32.0"
    "@typescript-eslint/typescript-estree": "npm:8.32.0"
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 10/df39111c374cffb36074fc1cb02ee08468c1f56ced8ff5ce47262add570a5a78f1d677759a7efa3e6d7840e97e0d1d5fae0dbca1737185c59fb3ef58e6be15d0
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.32.0":
  version: 8.32.0
  resolution: "@typescript-eslint/visitor-keys@npm:8.32.0"
  dependencies:
    "@typescript-eslint/types": "npm:8.32.0"
    eslint-visitor-keys: "npm:^4.2.0"
  checksum: 10/c2c3c94d17efc50655eb495b8324133cb646fe2464f7f99af571f62c2b09bca14b4713f2eeda0b2bcb2b0f4d54ec2641194a0d4b734607d93927476b93100810
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.0.0":
  version: 1.3.0
  resolution: "@ungap/structured-clone@npm:1.3.0"
  checksum: 10/80d6910946f2b1552a2406650051c91bbd1f24a6bf854354203d84fe2714b3e8ce4618f49cc3410494173a1c1e8e9777372fe68dce74bd45faf0a7a1a6ccf448
  languageName: node
  linkType: hard

"@unrs/resolver-binding-darwin-arm64@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-darwin-arm64@npm:1.7.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-darwin-x64@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-darwin-x64@npm:1.7.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-freebsd-x64@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-freebsd-x64@npm:1.7.2"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm-gnueabihf@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-linux-arm-gnueabihf@npm:1.7.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm-musleabihf@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-linux-arm-musleabihf@npm:1.7.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm64-gnu@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-linux-arm64-gnu@npm:1.7.2"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm64-musl@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-linux-arm64-musl@npm:1.7.2"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-ppc64-gnu@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-linux-ppc64-gnu@npm:1.7.2"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-riscv64-gnu@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-linux-riscv64-gnu@npm:1.7.2"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-riscv64-musl@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-linux-riscv64-musl@npm:1.7.2"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-s390x-gnu@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-linux-s390x-gnu@npm:1.7.2"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-x64-gnu@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-linux-x64-gnu@npm:1.7.2"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-x64-musl@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-linux-x64-musl@npm:1.7.2"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-wasm32-wasi@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-wasm32-wasi@npm:1.7.2"
  dependencies:
    "@napi-rs/wasm-runtime": "npm:^0.2.9"
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-arm64-msvc@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-win32-arm64-msvc@npm:1.7.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-ia32-msvc@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-win32-ia32-msvc@npm:1.7.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-x64-msvc@npm:1.7.2":
  version: 1.7.2
  resolution: "@unrs/resolver-binding-win32-x64-msvc@npm:1.7.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@vercel/analytics@npm:^1.4.1":
  version: 1.5.0
  resolution: "@vercel/analytics@npm:1.5.0"
  peerDependencies:
    "@remix-run/react": ^2
    "@sveltejs/kit": ^1 || ^2
    next: ">= 13"
    react: ^18 || ^19 || ^19.0.0-rc
    svelte: ">= 4"
    vue: ^3
    vue-router: ^4
  peerDependenciesMeta:
    "@remix-run/react":
      optional: true
    "@sveltejs/kit":
      optional: true
    next:
      optional: true
    react:
      optional: true
    svelte:
      optional: true
    vue:
      optional: true
    vue-router:
      optional: true
  checksum: 10/ae13f10585ffd458e2e00b7e3711f62fc5806447b119e3a6bc19cb1e8a1971527b434343e63207155bef5e4a170b7be6b83822ab94f4716df243cbc2a3fff3b4
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.14.1, @webassemblyjs/ast@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/ast@npm:1.14.1"
  dependencies:
    "@webassemblyjs/helper-numbers": "npm:1.13.2"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
  checksum: 10/f83e6abe38057f5d87c1fb356513a371a8b43c9b87657f2790741a66b1ef8ecf958d1391bc42f27c5fb33f58ab8286a38ea849fdd21f433cd4df1307424bab45
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.13.2"
  checksum: 10/e866ec8433f4a70baa511df5e8f2ebcd6c24f4e2cc6274c7c5aabe2bcce3459ea4680e0f35d450e1f3602acf3913b6b8e4f15069c8cfd34ae8609fb9a7d01795
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-api-error@npm:1.13.2"
  checksum: 10/48b5df7fd3095bb252f59a139fe2cbd999a62ac9b488123e9a0da3906ad8a2f2da7b2eb21d328c01a90da987380928706395c2897d1f3ed9e2125b6d75a920d0
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/helper-buffer@npm:1.14.1"
  checksum: 10/9690afeafa5e765a34620aa6216e9d40f9126d4e37e9726a2594bf60cab6b211ef20ab6670fd3c4449dd4a3497e69e49b2b725c8da0fb213208c7f45f15f5d5b
  languageName: node
  linkType: hard

"@webassemblyjs/helper-numbers@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-numbers@npm:1.13.2"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser": "npm:1.13.2"
    "@webassemblyjs/helper-api-error": "npm:1.13.2"
    "@xtuc/long": "npm:4.2.2"
  checksum: 10/e4c7d0b09811e1cda8eec644a022b560b28f4e974f50195375ccd007df5ee48a922a6dcff5ac40b6a8ec850d56d0ea6419318eee49fec7819ede14e90417a6a4
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.13.2"
  checksum: 10/3edd191fff7296df1ef3b023bdbe6cb5ea668f6386fd197ccfce46015c6f2a8cc9763cfb86503a0b94973ad27996645afff2252ee39a236513833259a47af6ed
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-buffer": "npm:1.14.1"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/wasm-gen": "npm:1.14.1"
  checksum: 10/6b73874f906532512371181d7088460f767966f26309e836060c5a8e4e4bfe6d523fb5f4c034b34aa22ebb1192815f95f0e264298769485c1f0980fdd63ae0ce
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/ieee754@npm:1.13.2"
  dependencies:
    "@xtuc/ieee754": "npm:^1.2.0"
  checksum: 10/d7e3520baa37a7309fa7db4d73d69fb869878853b1ebd4b168821bd03fcc4c0e1669c06231315b0039035d9a7a462e53de3ad982da4a426a4b0743b5888e8673
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/leb128@npm:1.13.2"
  dependencies:
    "@xtuc/long": "npm:4.2.2"
  checksum: 10/3a10542c86807061ec3230bac8ee732289c852b6bceb4b88ebd521a12fbcecec7c432848284b298154f28619e2746efbed19d6904aef06c49ef20a0b85f650cf
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.13.2":
  version: 1.13.2
  resolution: "@webassemblyjs/utf8@npm:1.13.2"
  checksum: 10/27885e5d19f339501feb210867d69613f281eda695ac508f04d69fa3398133d05b6870969c0242b054dc05420ed1cc49a64dea4fe0588c18d211cddb0117cc54
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-edit@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-buffer": "npm:1.14.1"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/helper-wasm-section": "npm:1.14.1"
    "@webassemblyjs/wasm-gen": "npm:1.14.1"
    "@webassemblyjs/wasm-opt": "npm:1.14.1"
    "@webassemblyjs/wasm-parser": "npm:1.14.1"
    "@webassemblyjs/wast-printer": "npm:1.14.1"
  checksum: 10/c62c50eadcf80876713f8c9f24106b18cf208160ab842fcb92060fd78c37bf37e7fcf0b7cbf1afc05d230277c2ce0f3f728432082c472dd1293e184a95f9dbdd
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-gen@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/ieee754": "npm:1.13.2"
    "@webassemblyjs/leb128": "npm:1.13.2"
    "@webassemblyjs/utf8": "npm:1.13.2"
  checksum: 10/6085166b0987d3031355fe17a4f9ef0f412e08098d95454059aced2bd72a4c3df2bc099fa4d32d640551fc3eca1ac1a997b44432e46dc9d84642688e42c17ed4
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-opt@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-buffer": "npm:1.14.1"
    "@webassemblyjs/wasm-gen": "npm:1.14.1"
    "@webassemblyjs/wasm-parser": "npm:1.14.1"
  checksum: 10/fa5d1ef8d2156e7390927f938f513b7fb4440dd6804b3d6c8622b7b1cf25a3abf1a5809f615896d4918e04b27b52bc3cbcf18faf2d563cb563ae0a9204a492db
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.14.1, @webassemblyjs/wasm-parser@npm:^1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wasm-parser@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@webassemblyjs/helper-api-error": "npm:1.13.2"
    "@webassemblyjs/helper-wasm-bytecode": "npm:1.13.2"
    "@webassemblyjs/ieee754": "npm:1.13.2"
    "@webassemblyjs/leb128": "npm:1.13.2"
    "@webassemblyjs/utf8": "npm:1.13.2"
  checksum: 10/07d9805fda88a893c984ed93d5a772d20d671e9731358ab61c6c1af8e0e58d1c42fc230c18974dfddebc9d2dd7775d514ba4d445e70080b16478b4b16c39c7d9
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.14.1":
  version: 1.14.1
  resolution: "@webassemblyjs/wast-printer@npm:1.14.1"
  dependencies:
    "@webassemblyjs/ast": "npm:1.14.1"
    "@xtuc/long": "npm:4.2.2"
  checksum: 10/cef09aad2fcd291bfcf9efdae2ea1e961a1ba0f925d1d9dcdd8c746d32fbaf431b6d26a0241699c0e39f82139018aa720b4ceb84ac6f4c78f13072747480db69
  languageName: node
  linkType: hard

"@xtuc/ieee754@npm:^1.2.0":
  version: 1.2.0
  resolution: "@xtuc/ieee754@npm:1.2.0"
  checksum: 10/ab033b032927d77e2f9fa67accdf31b1ca7440974c21c9cfabc8349e10ca2817646171c4f23be98d0e31896d6c2c3462a074fe37752e523abc3e45c79254259c
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.2":
  version: 4.2.2
  resolution: "@xtuc/long@npm:4.2.2"
  checksum: 10/7217bae9fe240e0d804969e7b2af11cb04ec608837c78b56ca88831991b287e232a0b7fce8d548beaff42aaf0197ffa471d81be6ac4c4e53b0148025a2c076ec
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10/ebd2c149dda6f543b66ce3779ea612151bb3aa9d0824f169773ee9876f1ca5a4e0adbcccc7eed048c04da7998e1825e2aa76fcca92d9e67dea50ac2b0a58dc2e
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: 10/d4371eaef7995530b5b5ca4183ff6f062ca17901a6d3f673c9ac011b01ede37e7a1f7f61f8f5cfe709e88054757bb8f3277dc4061087cdf4f2a1f90ccbcdb977
  languageName: node
  linkType: hard

"acorn@npm:^8.14.0, acorn@npm:^8.8.2, acorn@npm:^8.9.0":
  version: 8.14.1
  resolution: "acorn@npm:8.14.1"
  bin:
    acorn: bin/acorn
  checksum: 10/d1379bbee224e8d44c3c3946e6ba6973e999fbdd4e22e41c3455d7f9b6f72f7ce18d3dc218002e1e48eea789539cf1cb6d1430c81838c6744799c712fb557d92
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10/3db6d8d4651f2aa1a9e4af35b96ab11a7607af57a24f3bc721a387eaa3b5f674e901f0a648b0caefd48f3fd117c7761b79a3b55854e2aebaa96c3f32cf76af84
  languageName: node
  linkType: hard

"ajv-formats@npm:^2.1.1":
  version: 2.1.1
  resolution: "ajv-formats@npm:2.1.1"
  dependencies:
    ajv: "npm:^8.0.0"
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: 10/70c263ded219bf277ffd9127f793b625f10a46113b2e901e150da41931fcfd7f5592da6d66862f4449bb157ffe65867c3294a7df1d661cc232c4163d5a1718ed
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.5.2":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 10/d57c9d5bf8849bddcbd801b79bc3d2ddc736c2adb6b93a6a365429589dd7993ddbd5d37c6025ed6a7f89c27506b80131d5345c5b1fa6a97e40cd10a96bcd228c
  languageName: node
  linkType: hard

"ajv-keywords@npm:^5.1.0":
  version: 5.1.0
  resolution: "ajv-keywords@npm:5.1.0"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
  peerDependencies:
    ajv: ^8.8.2
  checksum: 10/5021f96ab7ddd03a4005326bd06f45f448ebfbb0fe7018b1b70b6c28142fa68372bda2057359814b83fd0b2d4c8726c297f0a7557b15377be7b56ce5344533d8
  languageName: node
  linkType: hard

"ajv@npm:^6.10.0, ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10/48d6ad21138d12eb4d16d878d630079a2bda25a04e745c07846a4ad768319533031e28872a9b3c5790fa1ec41aabdf2abed30a56e5a03ebc2cf92184b8ee306c
  languageName: node
  linkType: hard

"ajv@npm:^8.0.0, ajv@npm:^8.9.0":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: "npm:^3.1.3"
    fast-uri: "npm:^3.0.1"
    json-schema-traverse: "npm:^1.0.0"
    require-from-string: "npm:^2.0.2"
  checksum: 10/ee3c62162c953e91986c838f004132b6a253d700f1e51253b99791e2dbfdb39161bc950ebdc2f156f8568035bb5ed8be7bd78289cd9ecbf3381fe8f5b82e3f33
  languageName: node
  linkType: hard

"ansi-colors@npm:^4.1.3":
  version: 4.1.3
  resolution: "ansi-colors@npm:4.1.3"
  checksum: 10/43d6e2fc7b1c6e4dc373de708ee76311ec2e0433e7e8bd3194e7ff123ea6a747428fc61afdcf5969da5be3a5f0fd054602bec56fc0ebe249ce2fcde6e649e3c2
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10/2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10/495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10/b4494dfbfc7e4591b4711a396bd27e540f8153914123dccb4cdbbcb514015ada63a3809f362b9d8d4f6b17a706f1d7bea3c6f974b15fa5ae76b5b502070889ff
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10/70fdf883b704d17a5dfc9cde206e698c16bcd74e7f196ab821511651aee4f9f76c9514bdfa6ca3a27b5e49138b89cb222a28caf3afe4567570139577f991df32
  languageName: node
  linkType: hard

"any-promise@npm:^1.0.0":
  version: 1.3.0
  resolution: "any-promise@npm:1.3.0"
  checksum: 10/6737469ba353b5becf29e4dc3680736b9caa06d300bda6548812a8fee63ae7d336d756f88572fa6b5219aed36698d808fa55f62af3e7e6845c7a1dc77d240edb
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10/3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"arg@npm:^5.0.2":
  version: 5.0.2
  resolution: "arg@npm:5.0.2"
  checksum: 10/92fe7de222054a060fd2329e92e867410b3ea260328147ee3fb7855f78efae005f4087e698d4e688a856893c56bb09951588c40f2c901cf6996cd8cd7bcfef2c
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10/18640244e641a417ec75a9bd38b0b2b6b95af5199aa241b131d4b2fb206f334d7ecc600bd194861610a5579084978bfcbb02baa399dbe442d56d0ae5e60dbaef
  languageName: node
  linkType: hard

"aria-hidden@npm:^1.2.4":
  version: 1.2.4
  resolution: "aria-hidden@npm:1.2.4"
  dependencies:
    tslib: "npm:^2.0.0"
  checksum: 10/df4bc15423aaaba3729a7d40abcbf6d3fffa5b8fd5eb33d3ac8b7da0110c47552fca60d97f2e1edfbb68a27cae1da499f1c3896966efb3e26aac4e3b57e3cc8b
  languageName: node
  linkType: hard

"aria-query@npm:^5.3.2":
  version: 5.3.2
  resolution: "aria-query@npm:5.3.2"
  checksum: 10/b2fe9bc98bd401bc322ccb99717c1ae2aaf53ea0d468d6e7aebdc02fac736e4a99b46971ee05b783b08ade23c675b2d8b60e4a1222a95f6e27bc4d2a0bfdcc03
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    is-array-buffer: "npm:^3.0.5"
  checksum: 10/0ae3786195c3211b423e5be8dd93357870e6fb66357d81da968c2c39ef43583ef6eece1f9cb1caccdae4806739c65dea832b44b8593414313cd76a89795fca63
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.6, array-includes@npm:^3.1.8":
  version: 3.1.8
  resolution: "array-includes@npm:3.1.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    is-string: "npm:^1.0.7"
  checksum: 10/290b206c9451f181fb2b1f79a3bf1c0b66bb259791290ffbada760c79b284eef6f5ae2aeb4bcff450ebc9690edd25732c4c73a3c2b340fcc0f4563aed83bf488
  languageName: node
  linkType: hard

"array.prototype.findlast@npm:^1.2.5":
  version: 1.2.5
  resolution: "array.prototype.findlast@npm:1.2.5"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10/7dffcc665aa965718ad6de7e17ac50df0c5e38798c0a5bf9340cf24feb8594df6ec6f3fcbe714c1577728a1b18b5704b15669474b27bceeca91ef06ce2a23c31
  languageName: node
  linkType: hard

"array.prototype.findlastindex@npm:^1.2.5":
  version: 1.2.6
  resolution: "array.prototype.findlastindex@npm:1.2.6"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    es-shim-unscopables: "npm:^1.1.0"
  checksum: 10/5ddb6420e820bef6ddfdcc08ce780d0fd5e627e97457919c27e32359916de5a11ce12f7c55073555e503856618eaaa70845d6ca11dcba724766f38eb1c22f7a2
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.1, array.prototype.flat@npm:^1.3.2":
  version: 1.3.3
  resolution: "array.prototype.flat@npm:1.3.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10/f9b992fa0775d8f7c97abc91eb7f7b2f0ed8430dd9aeb9fdc2967ac4760cdd7fc2ef7ead6528fef40c7261e4d790e117808ce0d3e7e89e91514d4963a531cd01
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.2, array.prototype.flatmap@npm:^1.3.3":
  version: 1.3.3
  resolution: "array.prototype.flatmap@npm:1.3.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10/473534573aa4b37b1d80705d0ce642f5933cccf5617c9f3e8a56686e9815ba93d469138e86a1f25d2fe8af999c3d24f54d703ec1fc2db2e6778d46d0f4ac951e
  languageName: node
  linkType: hard

"array.prototype.tosorted@npm:^1.1.4":
  version: 1.1.4
  resolution: "array.prototype.tosorted@npm:1.1.4"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.3"
    es-errors: "npm:^1.3.0"
    es-shim-unscopables: "npm:^1.0.2"
  checksum: 10/874694e5d50e138894ff5b853e639c29b0aa42bbd355acda8e8e9cd337f1c80565f21edc15e8c727fa4c0877fd9d8783c575809e440cc4d2d19acaa048bf967d
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.1"
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    is-array-buffer: "npm:^3.0.4"
  checksum: 10/4821ebdfe7d699f910c7f09bc9fa996f09b96b80bccb4f5dd4b59deae582f6ad6e505ecef6376f8beac1eda06df2dbc89b70e82835d104d6fcabd33c1aed1ae9
  languageName: node
  linkType: hard

"ast-types-flow@npm:^0.0.8":
  version: 0.0.8
  resolution: "ast-types-flow@npm:0.0.8"
  checksum: 10/85a1c24af4707871c27cfe456bd2ff7fcbe678f3d1c878ac968c9557735a171a17bdcc8c8f903ceab3fc3c49d5b3da2194e6ab0a6be7fec0e133fa028f21ba1b
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 10/1a09379937d846f0ce7614e75071c12826945d4e417db634156bf0e4673c495989302f52186dfa9767a1d9181794554717badd193ca2bbab046ef1da741d8efd
  languageName: node
  linkType: hard

"autoprefixer@npm:^10.4.2":
  version: 10.4.21
  resolution: "autoprefixer@npm:10.4.21"
  dependencies:
    browserslist: "npm:^4.24.4"
    caniuse-lite: "npm:^1.0.30001702"
    fraction.js: "npm:^4.3.7"
    normalize-range: "npm:^0.1.2"
    picocolors: "npm:^1.1.1"
    postcss-value-parser: "npm:^4.2.0"
  peerDependencies:
    postcss: ^8.1.0
  bin:
    autoprefixer: bin/autoprefixer
  checksum: 10/5d7aeee78ef362a6838e12312908516a8ac5364414175273e5cff83bbff67612755b93d567f3aa01ce318342df48aeab4b291847b5800c780e58c458f61a98a6
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: "npm:^1.0.0"
  checksum: 10/6c9da3a66caddd83c875010a1ca8ef11eac02ba15fb592dc9418b2b5e7b77b645fa7729380a92d9835c2f05f2ca1b6251f39b993e0feb3f1517c74fa1af02cab
  languageName: node
  linkType: hard

"axe-core@npm:^4.10.0":
  version: 4.10.3
  resolution: "axe-core@npm:4.10.3"
  checksum: 10/9ff51ad0fd0fdec5c0247ea74e8ace5990b54c7f01f8fa3e5cd8ba98b0db24d8ebd7bab4a9bd4d75c28c4edcd1eac455b44c8c6c258c6a98f3d2f88bc60af4cc
  languageName: node
  linkType: hard

"axobject-query@npm:^4.1.0":
  version: 4.1.0
  resolution: "axobject-query@npm:4.1.0"
  checksum: 10/e275dea9b673f71170d914f2d2a18be5d57d8d29717b629e7fedd907dcc2ebdc7a37803ff975874810bd423f222f299c020d28fde40a146f537448bf6bfecb6e
  languageName: node
  linkType: hard

"babel-loader@npm:^8.2.3":
  version: 8.4.1
  resolution: "babel-loader@npm:8.4.1"
  dependencies:
    find-cache-dir: "npm:^3.3.1"
    loader-utils: "npm:^2.0.4"
    make-dir: "npm:^3.1.0"
    schema-utils: "npm:^2.6.5"
  peerDependencies:
    "@babel/core": ^7.0.0
    webpack: ">=2"
  checksum: 10/b54ae3796a351e5b5186cd7a8d53a902b679a33a187424c2858e385850142139a4343524c9fddd0eb4356250e51e67e47a9efeef79d01e132976c406212cba1d
  languageName: node
  linkType: hard

"bail@npm:^2.0.0":
  version: 2.0.2
  resolution: "bail@npm:2.0.2"
  checksum: 10/aab4e8ccdc8d762bf3fdfce8e706601695620c0c2eda256dd85088dc0be3cfd7ff126f6e99c2bee1f24f5d418414aacf09d7f9702f16d6963df2fa488cda8824
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10/9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"big.js@npm:^5.2.2":
  version: 5.2.2
  resolution: "big.js@npm:5.2.2"
  checksum: 10/c04416aeb084f4aa1c5857722439c327cc0ada9bd99ab80b650e3f30e2e4f1b92a04527ed1e7df8ffcd7c0ea311745a04af12d53e2f091bf09a06f1292003827
  languageName: node
  linkType: hard

"bignumber.js@npm:^9.1.2":
  version: 9.3.0
  resolution: "bignumber.js@npm:9.3.0"
  checksum: 10/60b79efcf7b56b925fca8eebd10d1f4b70aa2bf6eade7f5af0266f0092226dd2abcd9a3ee315ecb39459750d5a630ce3980b707e5d7bea32c97ffd378e8cc159
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10/bcad01494e8a9283abf18c1b967af65ee79b0c6a9e6fcfafebfe91dbe6e0fc7272bafb73389e198b310516ae04f7ad17d79aacf6cb4c0d5d5202a7e2e52c7d98
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10/faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10/a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10/fad11a0d4697a27162840b02b1fad249c1683cbc510cd5bf1a471f2f8085c046d41094308c577a50a03a579dd99d5a6b3724c4b5e8b14df2c4443844cfcda2c6
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0, browserslist@npm:^4.24.4":
  version: 4.24.5
  resolution: "browserslist@npm:4.24.5"
  dependencies:
    caniuse-lite: "npm:^1.0.30001716"
    electron-to-chromium: "npm:^1.5.149"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10/93fde829b77f20e2c4e1e0eaed154681c05e4828420e4afba790d480daa5de742977a44bbac8567881b8fbec3da3dea7ca1cb578ac1fd4385ef4ae91ca691d64
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10/0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"busboy@npm:1.6.0":
  version: 1.6.0
  resolution: "busboy@npm:1.6.0"
  dependencies:
    streamsearch: "npm:^1.1.0"
  checksum: 10/bee10fa10ea58e7e3e7489ffe4bda6eacd540a17de9f9cd21cc37e297b2dd9fe52b2715a5841afaec82900750d810d01d7edb4b2d456427f449b92b417579763
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10/ea026b27b13656330c2bbaa462a88181dcaa0435c1c2e705db89b31d9bdf7126049d6d0445ba746dca21454a0cfdf1d6f47fd39d34c8c8435296b30bc5738a13
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10/00482c1f6aa7cfb30fb1dbeb13873edf81cfac7c29ed67a5957d60635a56b2a4a480f1016ddbdb3395cc37900d46037fb965043a51c5c789ffeab4fc535d18b5
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.0"
    es-define-property: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.4"
    set-function-length: "npm:^1.2.2"
  checksum: 10/659b03c79bbfccf0cde3a79e7d52570724d7290209823e1ca5088f94b52192dc1836b82a324d0144612f816abb2f1734447438e38d9dafe0b3f82c2a1b9e3bce
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10/ef2b96e126ec0e58a7ff694db43f4d0d44f80e641370c21549ed911fecbdbc2df3ebc9bddad918d6bbdefeafb60bb3337902006d5176d72bcd2da74820991af7
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10/072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase-css@npm:^2.0.1":
  version: 2.0.1
  resolution: "camelcase-css@npm:2.0.1"
  checksum: 10/1cec2b3b3dcb5026688a470b00299a8db7d904c4802845c353dbd12d9d248d3346949a814d83bfd988d4d2e5b9904c07efe76fecd195a1d4f05b543e7c0b56b1
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001579, caniuse-lite@npm:^1.0.30001702, caniuse-lite@npm:^1.0.30001716":
  version: 1.0.30001717
  resolution: "caniuse-lite@npm:1.0.30001717"
  checksum: 10/e47dfd8707ea305baa177f3d3d531df614f5a9ac6335363fc8f86f0be4caf79f5734f3f68b601fee4edd9d79f1e5ffc0931466bb894bf955ed6b1dd5a1c34b1d
  languageName: node
  linkType: hard

"ccount@npm:^2.0.0":
  version: 2.0.1
  resolution: "ccount@npm:2.0.1"
  checksum: 10/48193dada54c9e260e0acf57fc16171a225305548f9ad20d5471e0f7a8c026aedd8747091dccb0d900cde7df4e4ddbd235df0d8de4a64c71b12f0d3303eeafd4
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10/cb3f3e594913d63b1814d7ca7c9bafbf895f75fbf93b92991980610dfd7b48500af4e3a5d4e3a8f337990a96b168d7eb84ee55efdce965e2ee8efc20f8c8f139
  languageName: node
  linkType: hard

"character-entities-html4@npm:^2.0.0":
  version: 2.1.0
  resolution: "character-entities-html4@npm:2.1.0"
  checksum: 10/7034aa7c7fa90309667f6dd50499c8a760c3d3a6fb159adb4e0bada0107d194551cdbad0714302f62d06ce4ed68565c8c2e15fdef2e8f8764eb63fa92b34b11d
  languageName: node
  linkType: hard

"character-entities-legacy@npm:^3.0.0":
  version: 3.0.0
  resolution: "character-entities-legacy@npm:3.0.0"
  checksum: 10/7582af055cb488b626d364b7d7a4e46b06abd526fb63c0e4eb35bcb9c9799cc4f76b39f34fdccef2d1174ac95e53e9ab355aae83227c1a2505877893fce77731
  languageName: node
  linkType: hard

"character-entities@npm:^2.0.0":
  version: 2.0.2
  resolution: "character-entities@npm:2.0.2"
  checksum: 10/c8dd1f4bf1a92fccf7d2fad9673660a88b37854557d30f6076c32fedfb92d1420208298829ff1d3b6b4fa1c7012e8326c45e7f5c3ed1e9a09ec177593c521b2f
  languageName: node
  linkType: hard

"character-reference-invalid@npm:^2.0.0":
  version: 2.0.1
  resolution: "character-reference-invalid@npm:2.0.1"
  checksum: 10/98d3b1a52ae510b7329e6ee7f6210df14f1e318c5415975d4c9e7ee0ef4c07875d47c6e74230c64551f12f556b4a8ccc24d9f3691a2aa197019e72a95e9297ee
  languageName: node
  linkType: hard

"chokidar@npm:^3.6.0":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10/c327fb07704443f8d15f7b4a7ce93b2f0bc0e6cea07ec28a7570aa22cd51fcf0379df589403976ea956c369f25aa82d84561947e227cd925902e1751371658df
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10/b63cb1f73d171d140a2ed8154ee6566c8ab775d3196b0e03a2a94b5f6a0ce7777ee5685ca56849403c8d17bd457a6540672f9a60696a6137c7a409097495b82c
  languageName: node
  linkType: hard

"chrome-trace-event@npm:^1.0.2":
  version: 1.0.4
  resolution: "chrome-trace-event@npm:1.0.4"
  checksum: 10/1762bed739774903bf5915fe3045c3120fc3c7f7d929d88e566447ea38944937a6370ccb687278318c43c24f837ad22dac780bed67c066336815557b8cf558c6
  languageName: node
  linkType: hard

"client-only@npm:0.0.1":
  version: 0.0.1
  resolution: "client-only@npm:0.0.1"
  checksum: 10/0c16bf660dadb90610553c1d8946a7fdfb81d624adea073b8440b7d795d5b5b08beb3c950c6a2cf16279365a3265158a236876d92bce16423c485c322d7dfaf8
  languageName: node
  linkType: hard

"clsx@npm:2.0.0":
  version: 2.0.0
  resolution: "clsx@npm:2.0.0"
  checksum: 10/943766d1b02fee3538c871e56638d87f973fbc2d6291ce221215ea436fdecb9be97ad323f411839c2d52c45640c449b1a53fbfe7e8b3d529b4e263308b630c9a
  languageName: node
  linkType: hard

"clsx@npm:^1.2.1":
  version: 1.2.1
  resolution: "clsx@npm:1.2.1"
  checksum: 10/5ded6f61f15f1fa0350e691ccec43a28b12fb8e64c8e94715f2a937bc3722d4c3ed41d6e945c971fc4dcc2a7213a43323beaf2e1c28654af63ba70c9968a8643
  languageName: node
  linkType: hard

"clsx@npm:^2.0.0":
  version: 2.1.1
  resolution: "clsx@npm:2.1.1"
  checksum: 10/cdfb57fa6c7649bbff98d9028c2f0de2f91c86f551179541cf784b1cfdc1562dcb951955f46d54d930a3879931a980e32a46b598acaea274728dbe068deca919
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10/fa00c91b4332b294de06b443923246bccebe9fab1b253f7fe1772d37b06a2269b4039a85e309abe1fe11b267b11c08d1d0473fda3badd6167f57313af2887a64
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10/b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: "npm:^1.0.0"
    simple-swizzle: "npm:^0.2.2"
  checksum: 10/72aa0b81ee71b3f4fb1ac9cd839cdbd7a011a7d318ef58e6cb13b3708dca75c7e45029697260488709f1b1c7ac4e35489a87e528156c1e365917d1c4ccb9b9cd
  languageName: node
  linkType: hard

"color@npm:^4.2.3":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: "npm:^2.0.1"
    color-string: "npm:^1.9.0"
  checksum: 10/b23f5e500a79ea22428db43d1a70642d983405c0dd1f95ef59dbdb9ba66afbb4773b334fa0b75bb10b0552fd7534c6b28d4db0a8b528f91975976e70973c0152
  languageName: node
  linkType: hard

"comma-separated-tokens@npm:^2.0.0":
  version: 2.0.3
  resolution: "comma-separated-tokens@npm:2.0.3"
  checksum: 10/e3bf9e0332a5c45f49b90e79bcdb4a7a85f28d6a6f0876a94f1bb9b2bfbdbbb9292aac50e1e742d8c0db1e62a0229a106f57917e2d067fca951d81737651700d
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: 10/90c5b6898610cd075984c58c4f88418a4fb44af08c1b1415e9854c03171bec31b336b7f3e4cefe33de994b3f12b03c5e2d638da4316df83593b9e82554e7e95b
  languageName: node
  linkType: hard

"commander@npm:^4.0.0":
  version: 4.1.1
  resolution: "commander@npm:4.1.1"
  checksum: 10/3b2dc4125f387dab73b3294dbcb0ab2a862f9c0ad748ee2b27e3544d25325b7a8cdfbcc228d103a98a716960b14478114a5206b5415bd48cdafa38797891562c
  languageName: node
  linkType: hard

"commondir@npm:^1.0.1":
  version: 1.0.1
  resolution: "commondir@npm:1.0.1"
  checksum: 10/4620bc4936a4ef12ce7dfcd272bb23a99f2ad68889a4e4ad766c9f8ad21af982511934d6f7050d4a8bde90011b1c15d56e61a1b4576d9913efbf697a20172d6c
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10/9680699c8e2b3af0ae22592cb764acaf973f292a7b71b8a06720233011853a58e256c89216a10cbe889727532fd77f8bcd49a760cedfde271b8e006c20e079f2
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10/c987be3ec061348cdb3c2bfb924bec86dea1eacad10550a85ca23edb0fe3556c3a61c7399114f3331ccb3499d7fd0285ab24566e5745929412983494c3926e15
  languageName: node
  linkType: hard

"copy-to-clipboard@npm:^3.3.3":
  version: 3.3.3
  resolution: "copy-to-clipboard@npm:3.3.3"
  dependencies:
    toggle-selection: "npm:^1.0.6"
  checksum: 10/e0a325e39b7615108e6c1c8ac110ae7b829cdc4ee3278b1df6a0e4228c490442cc86444cd643e2da344fbc424b3aab8909e2fec82f8bc75e7e5b190b7c24eecf
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10/0d52657d7ae36eb130999dffff1168ec348687b48dd38e2ff59992ed916c88d328cf1d07ff4a4a10bc78de5e1c23f04b306d569e42f7a2293915c081e4dfee86
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: 10/0e161912c1306861d8f46e1883be1cbc8b1b2879f0f509287c0db71796e4ddfb97ac96bdfca38f77f452e2c10554e1bb5678c99b07a5cf947a12778f73e47e12
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 10/f593cce41ff5ade23f44e77521e3a1bcc2c64107041e1bf6c3c32adc5187d0d60983292fda326154d20b01079e24931aa5b08e4467cc488b60bb1e7f6d478ade
  languageName: node
  linkType: hard

"cva@npm:1.0.0-beta.1":
  version: 1.0.0-beta.1
  resolution: "cva@npm:1.0.0-beta.1"
  dependencies:
    clsx: "npm:2.0.0"
  peerDependencies:
    typescript: ">= 4.5.5 < 6"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/2bf8e50a6f23dcc72d0e3f170ad3047ccfecea1c4d4d212875e18e3e7c3dc2527b5100a681ff18340881a32163e85d7d265687f450eb8c1035d78d4957346c0f
  languageName: node
  linkType: hard

"damerau-levenshtein@npm:^1.0.8":
  version: 1.0.8
  resolution: "damerau-levenshtein@npm:1.0.8"
  checksum: 10/f4eba1c90170f96be25d95fa3857141b5f81e254f7e4d530da929217b19990ea9a0390fc53d3c1cafac9152fda78e722ea4894f765cf6216be413b5af1fbf821
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10/c10b155a4e93999d3a215d08c23eea95f865e1f510b2e7748fcae1882b776df1afe8c99f483ace7fc0e5a3193ab08da138abebc9829d12003746c5a338c4d644
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.2"
  checksum: 10/2a47055fcf1ab3ec41b00b6f738c6461a841391a643c9ed9befec1117c1765b4d492661d97fb7cc899200c328949dca6ff189d2c6537d96d60e8a02dfe3c95f7
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-data-view: "npm:^1.0.1"
  checksum: 10/fa3bdfa0968bea6711ee50375094b39f561bce3f15f9e558df59de9c25f0bdd4cddc002d9c1d70ac7772ebd36854a7e22d1761e7302a934e6f1c2263bcf44aa2
  languageName: node
  linkType: hard

"debug@npm:4":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10/8e2709b2144f03c7950f8804d01ccb3786373df01e406a0f66928e47001cf2d336cbed9ee137261d4f90d68d8679468c755e3548ed83ddacdc82b194d2468afe
  languageName: node
  linkType: hard

"debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: "npm:^2.1.1"
  checksum: 10/d86fd7be2b85462297ea16f1934dc219335e802f629ca9a69b63ed8ed041dda492389bb2ee039217c02e5b54792b1c51aa96ae954cf28634d363a2360c7a1639
  languageName: node
  linkType: hard

"debug@npm:^4.0.0, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4, debug@npm:^4.4.0":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10/1847944c2e3c2c732514b93d11886575625686056cd765336212dc15de2d2b29612b6cd80e1afba767bb8e1803b778caf9973e98169ef1a24a7a7009e1820367
  languageName: node
  linkType: hard

"decimal.js@npm:^10.4.3":
  version: 10.5.0
  resolution: "decimal.js@npm:10.5.0"
  checksum: 10/714d49cf2f2207b268221795ede330e51452b7c451a0c02a770837d2d4faed47d603a729c2aa1d952eb6c4102d999e91c9b952c1aa016db3c5cba9fc8bf4cda2
  languageName: node
  linkType: hard

"decode-named-character-reference@npm:^1.0.0":
  version: 1.1.0
  resolution: "decode-named-character-reference@npm:1.1.0"
  dependencies:
    character-entities: "npm:^2.0.0"
  checksum: 10/102970fde2d011f307d3789776e68defd75ba4ade1a34951affd1fabb86cd32026fd809f2658c2b600d839a57b6b6a84e2b3a45166d38c8625d66ca11cd702b8
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10/ec12d074aef5ae5e81fa470b9317c313142c9e8e2afe3f8efa124db309720db96d1d222b82b84c834e5f87e7a614b44a4684b6683583118b87c833b3be40d4d8
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: "npm:^1.0.0"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.0.1"
  checksum: 10/abdcb2505d80a53524ba871273e5da75e77e52af9e15b3aa65d8aad82b8a3a424dad7aee2cc0b71470ac7acf501e08defac362e8b6a73cdb4309f028061df4ae
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3, define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.0"
    object-keys: "npm:^1.1.1"
  checksum: 10/b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"dequal@npm:^2.0.0":
  version: 2.0.3
  resolution: "dequal@npm:2.0.3"
  checksum: 10/6ff05a7561f33603df87c45e389c9ac0a95e3c056be3da1a0c4702149e3a7f6fe5ffbb294478687ba51a9e95f3a60e8b6b9005993acd79c292c7d15f71964b6b
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.3":
  version: 2.0.4
  resolution: "detect-libc@npm:2.0.4"
  checksum: 10/136e995f8c5ffbc515955b0175d441b967defd3d5f2268e89fa695e9c7170d8bed17993e31a34b04f0fad33d844a3a598e0fd519a8e9be3cad5f67662d96fee0
  languageName: node
  linkType: hard

"detect-node-es@npm:^1.1.0":
  version: 1.1.0
  resolution: "detect-node-es@npm:1.1.0"
  checksum: 10/e46307d7264644975b71c104b9f028ed1d3d34b83a15b8a22373640ce5ea630e5640b1078b8ea15f202b54641da71e4aa7597093bd4b91f113db520a26a37449
  languageName: node
  linkType: hard

"devlop@npm:^1.0.0, devlop@npm:^1.1.0":
  version: 1.1.0
  resolution: "devlop@npm:1.1.0"
  dependencies:
    dequal: "npm:^2.0.0"
  checksum: 10/3cc5f903d02d279d6dc4aa71ab6ed9898b9f4d1f861cc5421ce7357893c21b9520de78afb203c92bd650a6977ad0ca98195453a0707a39958cf5fea3b0a8ddd8
  languageName: node
  linkType: hard

"didyoumean@npm:^1.2.2":
  version: 1.2.2
  resolution: "didyoumean@npm:1.2.2"
  checksum: 10/de7f11b6a0c8c61018629b7f405bb9746d6e994ce87c1a4b7655c3c718442dc69037a3d46d804950604fd9cbe85c074f7b224a119fc1bda851690a74540c6cf8
  languageName: node
  linkType: hard

"dlv@npm:^1.1.3":
  version: 1.1.3
  resolution: "dlv@npm:1.1.3"
  checksum: 10/836459ec6b50e43e9ed388a5fc28954be99e3481af3fa4b5d82a600762eb65ef8faacd454097ed7fc2f8a60aea2800d65a4cece5cd0d81ab82b2031f3f759e6e
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: "npm:^2.0.2"
  checksum: 10/555684f77e791b17173ea86e2eea45ef26c22219cb64670669c4f4bebd26dbc95cd90ec1f4159e9349a6bb9eb892ce4dde8cd0139e77bedd8bf4518238618474
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: "npm:^2.0.2"
  checksum: 10/b4b28f1df5c563f7d876e7461254a4597b8cabe915abe94d7c5d1633fed263fcf9a85e8d3836591fc2d040108e822b0d32758e5ec1fe31c590dc7e08086e3e48
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10/5add88a3d68d42d6e6130a0cac450b7c2edbe73364bbd2fc334564418569bea97c6943a8fcd70e27130bf32afc236f30982fc4905039b703f23e9e0433c29934
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10/9b1d3e1baefeaf7d70799db8774149cef33b97183a6addceeba0cf6b85ba23ee2686f302f14482006df32df75d32b17c509c143a3689627929e4a8efaf483952
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.149":
  version: 1.5.150
  resolution: "electron-to-chromium@npm:1.5.150"
  checksum: 10/18c05019a00a2cc7747b39ef9bd95a9146d9fe0701636eedb3abc522d66a7b0415e4f58105d5df4c34f83f033f112dc88f9c24e0714e7059c247286ea392815f
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10/c72d67a6821be15ec11997877c437491c313d924306b8da5d87d2a2bcc2cec9903cb5b04ee1a088460501d8e5b44f10df82fdc93c444101a7610b80c8b6938e1
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10/915acf859cea7131dac1b2b5c9c8e35c4849e325a1d114c30adb8cd615970f6dca0e27f64f3a4949d7d6ed86ecd79a1c5c63f02e697513cddd7b5835c90948b8
  languageName: node
  linkType: hard

"emojis-list@npm:^3.0.0":
  version: 3.0.0
  resolution: "emojis-list@npm:3.0.0"
  checksum: 10/114f47d6d45612621497d2b1556c8f142c35332a591780a54e863e42d281e72d6c7d7c419f2e419319d4eb7f6ebf1db82d9744905d90f275db20d06a763b5e19
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10/bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.17.1":
  version: 5.18.1
  resolution: "enhanced-resolve@npm:5.18.1"
  dependencies:
    graceful-fs: "npm:^4.2.4"
    tapable: "npm:^2.2.0"
  checksum: 10/50e81c7fe2239fba5670ebce78a34709906ed3a79274aa416434f7307b252e0b7824d76a7dd403eca795571dc6afd9a44183fc45a68475e8f2fdfbae6e92fcc3
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10/65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10/1d20d825cdcce8d811bfbe86340f4755c02655a7feb2f13f8c880566d9d72a3f6c92c192a6867632e490d6da67b678271f46e01044996a6443e870331100dfdd
  languageName: node
  linkType: hard

"es-abstract@npm:^1.17.5, es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.3, es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.6, es-abstract@npm:^1.23.9":
  version: 1.23.9
  resolution: "es-abstract@npm:1.23.9"
  dependencies:
    array-buffer-byte-length: "npm:^1.0.2"
    arraybuffer.prototype.slice: "npm:^1.0.4"
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    data-view-buffer: "npm:^1.0.2"
    data-view-byte-length: "npm:^1.0.2"
    data-view-byte-offset: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    es-set-tostringtag: "npm:^2.1.0"
    es-to-primitive: "npm:^1.3.0"
    function.prototype.name: "npm:^1.1.8"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.0"
    get-symbol-description: "npm:^1.1.0"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    internal-slot: "npm:^1.1.0"
    is-array-buffer: "npm:^3.0.5"
    is-callable: "npm:^1.2.7"
    is-data-view: "npm:^1.0.2"
    is-regex: "npm:^1.2.1"
    is-shared-array-buffer: "npm:^1.0.4"
    is-string: "npm:^1.1.1"
    is-typed-array: "npm:^1.1.15"
    is-weakref: "npm:^1.1.0"
    math-intrinsics: "npm:^1.1.0"
    object-inspect: "npm:^1.13.3"
    object-keys: "npm:^1.1.1"
    object.assign: "npm:^4.1.7"
    own-keys: "npm:^1.0.1"
    regexp.prototype.flags: "npm:^1.5.3"
    safe-array-concat: "npm:^1.1.3"
    safe-push-apply: "npm:^1.0.0"
    safe-regex-test: "npm:^1.1.0"
    set-proto: "npm:^1.0.0"
    string.prototype.trim: "npm:^1.2.10"
    string.prototype.trimend: "npm:^1.0.9"
    string.prototype.trimstart: "npm:^1.0.8"
    typed-array-buffer: "npm:^1.0.3"
    typed-array-byte-length: "npm:^1.0.3"
    typed-array-byte-offset: "npm:^1.0.4"
    typed-array-length: "npm:^1.0.7"
    unbox-primitive: "npm:^1.1.0"
    which-typed-array: "npm:^1.1.18"
  checksum: 10/31a321966d760d88fc2ed984104841b42f4f24fc322b246002b9be0af162e03803ee41fcc3cf8be89e07a27ba3033168f877dd983703cb81422ffe5322a27582
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10/f8dc9e660d90919f11084db0a893128f3592b781ce967e4fccfb8f3106cb83e400a4032c559184ec52ee1dbd4b01e7776c7cd0b3327b1961b1a4a7008920fe78
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10/96e65d640156f91b707517e8cdc454dd7d47c32833aa3e85d79f24f9eb7ea85f39b63e36216ef0114996581969b59fe609a94e30316b08f5f4df1d44134cf8d5
  languageName: node
  linkType: hard

"es-iterator-helpers@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-iterator-helpers@npm:1.2.1"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.6"
    es-errors: "npm:^1.3.0"
    es-set-tostringtag: "npm:^2.0.3"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.6"
    globalthis: "npm:^1.0.4"
    gopd: "npm:^1.2.0"
    has-property-descriptors: "npm:^1.0.2"
    has-proto: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    internal-slot: "npm:^1.1.0"
    iterator.prototype: "npm:^1.1.4"
    safe-array-concat: "npm:^1.1.3"
  checksum: 10/802e0e8427a05ff4a5b0c70c7fdaaeff37cdb81a28694aeb7bfb831c6ab340d8f3deeb67b96732ff9e9699ea240524d5ea8a9a6a335fcd15aa3983b27b06113f
  languageName: node
  linkType: hard

"es-module-lexer@npm:^1.2.1":
  version: 1.7.0
  resolution: "es-module-lexer@npm:1.7.0"
  checksum: 10/b6f3e576a3fed4d82b0d0ad4bbf6b3a5ad694d2e7ce8c4a069560da3db6399381eaba703616a182b16dde50ce998af64e07dcf49f2ae48153b9e07be3f107087
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10/54fe77de288451dae51c37bfbfe3ec86732dc3778f98f3eb3bdb4bf48063b2c0b8f9c93542656986149d08aa5be3204286e2276053d19582b76753f1a2728867
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.3, es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10/86814bf8afbcd8966653f731415888019d4bc4aca6b6c354132a7a75bb87566751e320369654a101d23a91c87a85c79b178bcf40332839bd347aff437c4fb65f
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.2, es-shim-unscopables@npm:^1.1.0":
  version: 1.1.0
  resolution: "es-shim-unscopables@npm:1.1.0"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10/c351f586c30bbabc62355be49564b2435468b52c3532b8a1663672e3d10dc300197e69c247869dd173e56d86423ab95fc0c10b0939cdae597094e0fdca078cba
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: "npm:^1.2.7"
    is-date-object: "npm:^1.0.5"
    is-symbol: "npm:^1.0.4"
  checksum: 10/17faf35c221aad59a16286cbf58ef6f080bf3c485dff202c490d074d8e74da07884e29b852c245d894eac84f73c58330ec956dfd6d02c0b449d75eb1012a3f9b
  languageName: node
  linkType: hard

"escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10/9d7169e3965b2f9ae46971afa392f6e5a25545ea30f2e2dd99c9b0a95a3f52b5653681a84f5b2911a413ddad2d7a93d3514165072f349b5ffc59c75a899970d6
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 10/98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-config-next@npm:15.0.1":
  version: 15.0.1
  resolution: "eslint-config-next@npm:15.0.1"
  dependencies:
    "@next/eslint-plugin-next": "npm:15.0.1"
    "@rushstack/eslint-patch": "npm:^1.10.3"
    "@typescript-eslint/eslint-plugin": "npm:^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0"
    "@typescript-eslint/parser": "npm:^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0"
    eslint-import-resolver-node: "npm:^0.3.6"
    eslint-import-resolver-typescript: "npm:^3.5.2"
    eslint-plugin-import: "npm:^2.31.0"
    eslint-plugin-jsx-a11y: "npm:^6.10.0"
    eslint-plugin-react: "npm:^7.35.0"
    eslint-plugin-react-hooks: "npm:^5.0.0"
  peerDependencies:
    eslint: ^7.23.0 || ^8.0.0 || ^9.0.0
    typescript: ">=3.3.1"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 10/5905f588adeb7d5d007d9fcd033d7919a6b19d5b9f6f841fa5a06e9d6a1cf50ba38330b5923f1c3134a018d19621e21f0d10ab9baa4f318360ff017d71d1c820
  languageName: node
  linkType: hard

"eslint-import-resolver-node@npm:^0.3.6, eslint-import-resolver-node@npm:^0.3.9":
  version: 0.3.9
  resolution: "eslint-import-resolver-node@npm:0.3.9"
  dependencies:
    debug: "npm:^3.2.7"
    is-core-module: "npm:^2.13.0"
    resolve: "npm:^1.22.4"
  checksum: 10/d52e08e1d96cf630957272e4f2644dcfb531e49dcfd1edd2e07e43369eb2ec7a7d4423d417beee613201206ff2efa4eb9a582b5825ee28802fc7c71fcd53ca83
  languageName: node
  linkType: hard

"eslint-import-resolver-typescript@npm:^3.5.2":
  version: 3.10.1
  resolution: "eslint-import-resolver-typescript@npm:3.10.1"
  dependencies:
    "@nolyfill/is-core-module": "npm:1.0.39"
    debug: "npm:^4.4.0"
    get-tsconfig: "npm:^4.10.0"
    is-bun-module: "npm:^2.0.0"
    stable-hash: "npm:^0.0.5"
    tinyglobby: "npm:^0.2.13"
    unrs-resolver: "npm:^1.6.2"
  peerDependencies:
    eslint: "*"
    eslint-plugin-import: "*"
    eslint-plugin-import-x: "*"
  peerDependenciesMeta:
    eslint-plugin-import:
      optional: true
    eslint-plugin-import-x:
      optional: true
  checksum: 10/b8d6a9b2045c70f043f722f78c9e65bc0283126f0ad92c8f07473f7647d77f7b1562f765a472f17e06b81897b407091c0ec9f2e4592b158c9fd92d0b0c33de89
  languageName: node
  linkType: hard

"eslint-module-utils@npm:^2.12.0":
  version: 2.12.0
  resolution: "eslint-module-utils@npm:2.12.0"
  dependencies:
    debug: "npm:^3.2.7"
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: 10/dd27791147eca17366afcb83f47d6825b6ce164abb256681e5de4ec1d7e87d8605641eb869298a0dbc70665e2446dbcc2f40d3e1631a9475dd64dd23d4ca5dee
  languageName: node
  linkType: hard

"eslint-plugin-import@npm:^2.31.0":
  version: 2.31.0
  resolution: "eslint-plugin-import@npm:2.31.0"
  dependencies:
    "@rtsao/scc": "npm:^1.1.0"
    array-includes: "npm:^3.1.8"
    array.prototype.findlastindex: "npm:^1.2.5"
    array.prototype.flat: "npm:^1.3.2"
    array.prototype.flatmap: "npm:^1.3.2"
    debug: "npm:^3.2.7"
    doctrine: "npm:^2.1.0"
    eslint-import-resolver-node: "npm:^0.3.9"
    eslint-module-utils: "npm:^2.12.0"
    hasown: "npm:^2.0.2"
    is-core-module: "npm:^2.15.1"
    is-glob: "npm:^4.0.3"
    minimatch: "npm:^3.1.2"
    object.fromentries: "npm:^2.0.8"
    object.groupby: "npm:^1.0.3"
    object.values: "npm:^1.2.0"
    semver: "npm:^6.3.1"
    string.prototype.trimend: "npm:^1.0.8"
    tsconfig-paths: "npm:^3.15.0"
  peerDependencies:
    eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
  checksum: 10/6b76bd009ac2db0615d9019699d18e2a51a86cb8c1d0855a35fb1b418be23b40239e6debdc6e8c92c59f1468ed0ea8d7b85c817117a113d5cc225be8a02ad31c
  languageName: node
  linkType: hard

"eslint-plugin-jsx-a11y@npm:^6.10.0":
  version: 6.10.2
  resolution: "eslint-plugin-jsx-a11y@npm:6.10.2"
  dependencies:
    aria-query: "npm:^5.3.2"
    array-includes: "npm:^3.1.8"
    array.prototype.flatmap: "npm:^1.3.2"
    ast-types-flow: "npm:^0.0.8"
    axe-core: "npm:^4.10.0"
    axobject-query: "npm:^4.1.0"
    damerau-levenshtein: "npm:^1.0.8"
    emoji-regex: "npm:^9.2.2"
    hasown: "npm:^2.0.2"
    jsx-ast-utils: "npm:^3.3.5"
    language-tags: "npm:^1.0.9"
    minimatch: "npm:^3.1.2"
    object.fromentries: "npm:^2.0.8"
    safe-regex-test: "npm:^1.0.3"
    string.prototype.includes: "npm:^2.0.1"
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9
  checksum: 10/388550798548d911e2286d530a29153ca00434a06fcfc0e31e0dda46a5e7960005e532fb29ce1ccbf1e394a3af3e5cf70c47ca43778861eacc5e3ed799adb79c
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:^5.0.0":
  version: 5.2.0
  resolution: "eslint-plugin-react-hooks@npm:5.2.0"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0
  checksum: 10/ebb79e9cf69ae06e3a7876536653c5e556b5fd8cd9dc49577f10a6e728360e7b6f5ce91f4339b33e93b26e3bb23805418f8b5e75db80baddd617b1dffe73bed1
  languageName: node
  linkType: hard

"eslint-plugin-react@npm:^7.35.0":
  version: 7.37.5
  resolution: "eslint-plugin-react@npm:7.37.5"
  dependencies:
    array-includes: "npm:^3.1.8"
    array.prototype.findlast: "npm:^1.2.5"
    array.prototype.flatmap: "npm:^1.3.3"
    array.prototype.tosorted: "npm:^1.1.4"
    doctrine: "npm:^2.1.0"
    es-iterator-helpers: "npm:^1.2.1"
    estraverse: "npm:^5.3.0"
    hasown: "npm:^2.0.2"
    jsx-ast-utils: "npm:^2.4.1 || ^3.0.0"
    minimatch: "npm:^3.1.2"
    object.entries: "npm:^1.1.9"
    object.fromentries: "npm:^2.0.8"
    object.values: "npm:^1.2.1"
    prop-types: "npm:^15.8.1"
    resolve: "npm:^2.0.0-next.5"
    semver: "npm:^6.3.1"
    string.prototype.matchall: "npm:^4.0.12"
    string.prototype.repeat: "npm:^1.0.0"
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7
  checksum: 10/ee1bd4e0ec64f29109d5a625bb703d179c82e0159c86c3f1b52fc1209d2994625a137dae303c333fb308a2e38315e44066d5204998177e31974382f9fda25d5c
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^4.1.1"
  checksum: 10/c541ef384c92eb5c999b7d3443d80195fcafb3da335500946f6db76539b87d5826c8f2e1d23bf6afc3154ba8cd7c8e566f8dc00f1eea25fdf3afc8fb9c87b238
  languageName: node
  linkType: hard

"eslint-scope@npm:^7.1.1":
  version: 7.2.2
  resolution: "eslint-scope@npm:7.2.2"
  dependencies:
    esrecurse: "npm:^4.3.0"
    estraverse: "npm:^5.2.0"
  checksum: 10/5c660fb905d5883ad018a6fea2b49f3cb5b1cbf2cd4bd08e98646e9864f9bc2c74c0839bed2d292e90a4a328833accc197c8f0baed89cbe8d605d6f918465491
  languageName: node
  linkType: hard

"eslint-utils@npm:^3.0.0":
  version: 3.0.0
  resolution: "eslint-utils@npm:3.0.0"
  dependencies:
    eslint-visitor-keys: "npm:^2.0.0"
  peerDependencies:
    eslint: ">=5"
  checksum: 10/7675260a6b220c70f13e4cdbf077e93cad0dfb388429a27d6c0b584b2b20dca24594508e8bdb00a460a5764bd364a5018e20c2b8b1d70f82bcc3fdc30692a4d2
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^2.0.0":
  version: 2.1.0
  resolution: "eslint-visitor-keys@npm:2.1.0"
  checksum: 10/db4547eef5039122d518fa307e938ceb8589da5f6e8f5222efaf14dd62f748ce82e2d2becd3ff9412a50350b726bda95dbea8515a471074547daefa58aee8735
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 10/3f357c554a9ea794b094a09bd4187e5eacd1bc0d0653c3adeb87962c548e6a1ab8f982b86963ae1337f5d976004146536dcee5d0e2806665b193fbfbf1a9231b
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.0":
  version: 4.2.0
  resolution: "eslint-visitor-keys@npm:4.2.0"
  checksum: 10/9651b3356b01760e586b4c631c5268c0e1a85236e3292bf754f0472f465bf9a856c0ddc261fceace155334118c0151778effafbab981413dbf9288349343fa25
  languageName: node
  linkType: hard

"eslint@npm:8.10.0":
  version: 8.10.0
  resolution: "eslint@npm:8.10.0"
  dependencies:
    "@eslint/eslintrc": "npm:^1.2.0"
    "@humanwhocodes/config-array": "npm:^0.9.2"
    ajv: "npm:^6.10.0"
    chalk: "npm:^4.0.0"
    cross-spawn: "npm:^7.0.2"
    debug: "npm:^4.3.2"
    doctrine: "npm:^3.0.0"
    escape-string-regexp: "npm:^4.0.0"
    eslint-scope: "npm:^7.1.1"
    eslint-utils: "npm:^3.0.0"
    eslint-visitor-keys: "npm:^3.3.0"
    espree: "npm:^9.3.1"
    esquery: "npm:^1.4.0"
    esutils: "npm:^2.0.2"
    fast-deep-equal: "npm:^3.1.3"
    file-entry-cache: "npm:^6.0.1"
    functional-red-black-tree: "npm:^1.0.1"
    glob-parent: "npm:^6.0.1"
    globals: "npm:^13.6.0"
    ignore: "npm:^5.2.0"
    import-fresh: "npm:^3.0.0"
    imurmurhash: "npm:^0.1.4"
    is-glob: "npm:^4.0.0"
    js-yaml: "npm:^4.1.0"
    json-stable-stringify-without-jsonify: "npm:^1.0.1"
    levn: "npm:^0.4.1"
    lodash.merge: "npm:^4.6.2"
    minimatch: "npm:^3.0.4"
    natural-compare: "npm:^1.4.0"
    optionator: "npm:^0.9.1"
    regexpp: "npm:^3.2.0"
    strip-ansi: "npm:^6.0.1"
    strip-json-comments: "npm:^3.1.0"
    text-table: "npm:^0.2.0"
    v8-compile-cache: "npm:^2.0.3"
  bin:
    eslint: bin/eslint.js
  checksum: 10/34fa4909fc278fcb860e0500d374fdee0115761f45f6880fd23a7c9bf2941875e7fb40f0319f2eb20a5d0ba95f7bd1f1653d7a595fc0161ba5da76a2a4b23cb0
  languageName: node
  linkType: hard

"espree@npm:^9.3.1, espree@npm:^9.4.0":
  version: 9.6.1
  resolution: "espree@npm:9.6.1"
  dependencies:
    acorn: "npm:^8.9.0"
    acorn-jsx: "npm:^5.3.2"
    eslint-visitor-keys: "npm:^3.4.1"
  checksum: 10/255ab260f0d711a54096bdeda93adff0eadf02a6f9b92f02b323e83a2b7fc258797919437ad331efec3930475feb0142c5ecaaf3cdab4befebd336d47d3f3134
  languageName: node
  linkType: hard

"esquery@npm:^1.4.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: "npm:^5.1.0"
  checksum: 10/c587fb8ec9ed83f2b1bc97cf2f6854cc30bf784a79d62ba08c6e358bf22280d69aee12827521cf38e69ae9761d23fb7fde593ce315610f85655c139d99b05e5a
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: "npm:^5.2.0"
  checksum: 10/44ffcd89e714ea6b30143e7f119b104fc4d75e77ee913f34d59076b40ef2d21967f84e019f84e1fd0465b42cdbf725db449f232b5e47f29df29ed76194db8e16
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: 10/3f67ad02b6dbfaddd9ea459cf2b6ef4ecff9a6082a7af9d22e445b9abc082ad9ca47e1825557b293fcdae477f4714e561123e30bb6a5b2f184fb2bad4a9497eb
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0, estraverse@npm:^5.3.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 10/37cbe6e9a68014d34dbdc039f90d0baf72436809d02edffcc06ba3c2a12eb298048f877511353b130153e532aac8d68ba78430c0dd2f44806ebc7c014b01585e
  languageName: node
  linkType: hard

"estree-util-is-identifier-name@npm:^3.0.0":
  version: 3.0.0
  resolution: "estree-util-is-identifier-name@npm:3.0.0"
  checksum: 10/cdc9187614fdb269d714eddfdf72c270a79daa9ed51e259bb78527983be6dcc68da6a914ccc41175b662194c67fbd2a1cd262f85fac1eef7111cfddfaf6f77f8
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 10/b23acd24791db11d8f65be5ea58fd9a6ce2df5120ae2da65c16cfc5331ff59d5ac4ef50af66cd4bde238881503ec839928a0135b99a036a9cdfa22d17fd56cdb
  languageName: node
  linkType: hard

"events@npm:^3.2.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: 10/a3d47e285e28d324d7180f1e493961a2bbb4cad6412090e4dec114f4db1f5b560c7696ee8e758f55e23913ede856e3689cd3aa9ae13c56b5d8314cd3b3ddd1be
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10/ca2f01f1aa4dafd3f3917bd531ab5be08c6f5f4b2389d2e974f903de3cbeb50b9633374353516b6afd70905775e33aba11afab1232d3acf0aa2963b98a611c51
  languageName: node
  linkType: hard

"extend@npm:^3.0.0":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: 10/59e89e2dc798ec0f54b36d82f32a27d5f6472c53974f61ca098db5d4648430b725387b53449a34df38fd0392045434426b012f302b3cc049a6500ccf82877e4e
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10/e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-glob@npm:3.3.1":
  version: 3.3.1
  resolution: "fast-glob@npm:3.3.1"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.4"
  checksum: 10/51bcd15472879dfe51d4b01c5b70bbc7652724d39cdd082ba11276dbd7d84db0f6b33757e1938af8b2768a4bf485d9be0c89153beae24ee8331d6dcc7550379f
  languageName: node
  linkType: hard

"fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10/dcc6432b269762dd47381d8b8358bf964d8f4f60286ac6aa41c01ade70bda459ff2001b516690b96d5365f68a49242966112b5d5cc9cd82395fa8f9d017c90ad
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10/2c20055c1fa43c922428f16ca8bb29f2807de63e5c851f665f7ac9790176c01c3b40335257736b299764a8d383388dabc73c8083b8e1bc3d99f0a941444ec60e
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 10/eb7e220ecf2bab5159d157350b81d01f75726a4382f5a9266f42b9150c4523b9795f7f5d9fbbbeaeac09a441b2369f05ee02db48ea938584205530fe5693cfe1
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.6
  resolution: "fast-uri@npm:3.0.6"
  checksum: 10/43c87cd03926b072a241590e49eca0e2dfe1d347ddffd4b15307613b42b8eacce00a315cf3c7374736b5f343f27e27ec88726260eb03a758336d507d6fbaba0a
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10/75679dc226316341c4f2a6b618571f51eac96779906faecd8921b984e844d6ae42fabb2df69b1071327d398d5716693ea9c9c8941f64ac9e89ec2032ce59d730
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.4
  resolution: "fdir@npm:6.4.4"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10/d0000d6b790059b35f4ed19acc8847a66452e0bc68b28766c929ffd523e5ec2083811fc8a545e4a1d4945ce70e887b3a610c145c681073b506143ae3076342ed
  languageName: node
  linkType: hard

"fetch-event-stream@npm:^0.1.5":
  version: 0.1.5
  resolution: "fetch-event-stream@npm:0.1.5"
  checksum: 10/06411c4ab6f86bd3093fab79c92e14083cf0e15f91e6546a38ed9d6eff48f9d06e48e19c9e371b1f0317d93a21eef7c65d600b20120d15eca7e66e2aeb91d04c
  languageName: node
  linkType: hard

"file-entry-cache@npm:^6.0.1":
  version: 6.0.1
  resolution: "file-entry-cache@npm:6.0.1"
  dependencies:
    flat-cache: "npm:^3.0.4"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10/a7095cb39e5bc32fada2aa7c7249d3f6b01bd1ce461a61b0adabacccabd9198500c6fb1f68a7c851a657e273fce2233ba869638897f3d7ed2e87a2d89b4436ea
  languageName: node
  linkType: hard

"find-cache-dir@npm:^3.3.1":
  version: 3.3.2
  resolution: "find-cache-dir@npm:3.3.2"
  dependencies:
    commondir: "npm:^1.0.1"
    make-dir: "npm:^3.0.2"
    pkg-dir: "npm:^4.1.0"
  checksum: 10/3907c2e0b15132704ed67083686cd3e68ab7d9ecc22e50ae9da20678245d488b01fa22c0e34c0544dc6edc4354c766f016c8c186a787be7c17f7cde8c5281e85
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: "npm:^5.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10/4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"flat-cache@npm:^3.0.4":
  version: 3.2.0
  resolution: "flat-cache@npm:3.2.0"
  dependencies:
    flatted: "npm:^3.2.9"
    keyv: "npm:^4.5.3"
    rimraf: "npm:^3.0.2"
  checksum: 10/02381c6ece5e9fa5b826c9bbea481d7fd77645d96e4b0b1395238124d581d10e56f17f723d897b6d133970f7a57f0fab9148cbbb67237a0a0ffe794ba60c0c70
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: 10/********************************************************************************************************************************
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: "npm:^1.2.7"
  checksum: 10/330cc2439f85c94f4609de3ee1d32c5693ae15cdd7fe3d112c4fd9efd4ce7143f2c64ef6c2c9e0cfdb0058437f33ef05b5bdae5b98fcc903fb2143fbaf0fea0f
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10/427b33f997a98073c0424e5c07169264a62cda806d8d2ded159b5b903fdfc8f0a1457e06b5fc35506497acb3f1e353f025edee796300209ac6231e80edece835
  languageName: node
  linkType: hard

"fraction.js@npm:^4.3.7":
  version: 4.3.7
  resolution: "fraction.js@npm:4.3.7"
  checksum: 10/bb5ebcdeeffcdc37b68ead3bdfc244e68de188e0c64e9702197333c72963b95cc798883ad16adc21588088b942bca5b6a6ff4aeb1362d19f6f3b629035dc15f5
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/af143246cf6884fe26fa281621d45cfe111d34b30535a475bfa38dafe343dadb466c047a924ffc7d6b7b18265df4110224ce3803806dbb07173bf2087b648d7f
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10/e703107c28e362d8d7b910bbcbfd371e640a3bb45ae157a362b5952c0030c0b6d4981140ec319b347bce7adc025dd7813da1ff908a945ac214d64f5402a51b96
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10/4c1ade961ded57cdbfbb5cac5106ec17bc8bccd62e16343c569a0ceeca83b9dfef87550b4dc5cbb89642da412b20c5071f304c8c464b80415446e8e155a038c0
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10/185e20d20f10c8d661d59aac0f3b63b31132d492e1b11fcc2a93cb2c47257ebaee7407c38513efd2b35cafdf972d9beb2ea4593c1e0f3bf8f2744836928d7454
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    functions-have-names: "npm:^1.2.3"
    hasown: "npm:^2.0.2"
    is-callable: "npm:^1.2.7"
  checksum: 10/25b9e5bea936732a6f0c0c08db58cc0d609ac1ed458c6a07ead46b32e7b9bf3fe5887796c3f83d35994efbc4fdde81c08ac64135b2c399b8f2113968d44082bc
  languageName: node
  linkType: hard

"functional-red-black-tree@npm:^1.0.1":
  version: 1.0.1
  resolution: "functional-red-black-tree@npm:1.0.1"
  checksum: 10/debe73e92204341d1fa5f89614e44284d3add26dee660722978d8c50829170f87d1c74768f68c251d215ae461c11db7bac13101c77f4146ff051da75466f7a12
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: 10/0ddfd3ed1066a55984aaecebf5419fbd9344a5c38dd120ffb0739fac4496758dcf371297440528b115e4367fc46e3abc86a2cc0ff44612181b175ae967a11a05
  languageName: node
  linkType: hard

"geist@npm:^1.3.1":
  version: 1.4.2
  resolution: "geist@npm:1.4.2"
  peerDependencies:
    next: ">=13.2.0"
  checksum: 10/18e5eb0e4f0cedb870b907803dfcf801d4f464bf7f832105fdcd6ead0dbcde2c798f8f41422a4de3c41203c16082dc2ab16d4dda1c44a074f12c085f8f332445
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10/17d8333460204fbf1f9160d067e1e77f908a5447febb49424b8ab043026049835c9ef3974445c57dbd39161f4d2b04356d7de12b2eecaa27a7a7ea7d871cbedd
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10/6e9dd920ff054147b6f44cb98104330e87caafae051b6d37b13384a45ba15e71af33c3baeac7cb630a0aaa23142718dcf25b45cfdd86c184c5dcb4e56d953a10
  languageName: node
  linkType: hard

"get-nonce@npm:^1.0.0":
  version: 1.0.1
  resolution: "get-nonce@npm:1.0.1"
  checksum: 10/ad5104871d114a694ecc506a2d406e2331beccb961fe1e110dc25556b38bcdbf399a823a8a375976cd8889668156a9561e12ebe3fa6a4c6ba169c8466c2ff868
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10/a353e3a9595a74720b40fb5bae3ba4a4f826e186e83814d93375182384265676f59e49998b9cdfac4a2225ce95a3d32a68f502a2c5619303987f1c183ab80494
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.10.0":
  version: 4.10.0
  resolution: "get-tsconfig@npm:4.10.0"
  dependencies:
    resolve-pkg-maps: "npm:^1.0.0"
  checksum: 10/5259b5c99a1957114337d9d0603b4a305ec9e29fa6cac7d2fbf634ba6754a0cc88bfd281a02416ce64e604b637d3cb239185381a79a5842b17fb55c097b38c4b
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10/32cd106ce8c0d83731966d31517adb766d02c3812de49c30cfe0675c7c0ae6630c11214c54a5ae67aca882cf738d27fd7768f21aa19118b9245950554be07247
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.1, glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: "npm:^4.0.3"
  checksum: 10/c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.4.1":
  version: 0.4.1
  resolution: "glob-to-regexp@npm:0.4.1"
  checksum: 10/9009529195a955c40d7b9690794aeff5ba665cc38f1519e111c58bb54366fd0c106bde80acf97ba4e533208eb53422c83b136611a54c5fefb1edd8dc267cb62e
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10/698dfe11828b7efd0514cd11e573eaed26b2dff611f0400907281ce3eab0c1e56143ef9b35adc7c77ecc71fba74717b510c7c223d34ca8a98ec81777b293d4ac
  languageName: node
  linkType: hard

"glob@npm:^7.1.3":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10/59452a9202c81d4508a43b8af7082ca5c76452b9fcc4a9ab17655822e6ce9b21d4f8fbadabe4fe3faef448294cec249af305e2cd824b7e9aaf689240e5e96a7b
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10/9f054fa38ff8de8fa356502eb9d2dae0c928217b8b5c8de1f09f5c9b6c8a96d8b9bd3afc49acbcd384a98a81fea713c859e1b09e214c60509517bb8fc2bc13c2
  languageName: node
  linkType: hard

"globals@npm:^13.19.0, globals@npm:^13.6.0":
  version: 13.24.0
  resolution: "globals@npm:13.24.0"
  dependencies:
    type-fest: "npm:^0.20.2"
  checksum: 10/62c5b1997d06674fc7191d3e01e324d3eda4d65ac9cc4e78329fa3b5c4fd42a0e1c8722822497a6964eee075255ce21ccf1eec2d83f92ef3f06653af4d0ee28e
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: "npm:^1.2.1"
    gopd: "npm:^1.0.1"
  checksum: 10/1f1fd078fb2f7296306ef9dd51019491044ccf17a59ed49d375b576ca108ff37e47f3d29aead7add40763574a992f16a5367dd1e2173b8634ef18556ab719ac4
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10/94e296d69f92dc1c0768fcfeecfb3855582ab59a7c75e969d5f96ce50c3d201fd86d5a2857c22565764d5bb8a816c7b1e58f133ec318cd56274da36c5e3fb1a1
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.2.11, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10/bf152d0ed1dc159239db1ba1f74fdbc40cb02f626770dcd5815c427ce0688c2635a06ed69af364396da4636d0408fcf7d4afdf7881724c3307e46aff30ca49e2
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: 10/6dd60dba97007b21e3a829fab3f771803cc1292977fe610e240ea72afd67e5690ac9eeaafc4a99710e78962e5936ab5a460787c2a1180f1cb0ccfac37d29f897
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 10/90fb1b24d40d2472bcd1c8bd9dd479037ec240215869bdbff97b2be83acef57d28f7e96bdd003a21bed218d058b49097f4acc8821c05b1629cc5d48dd7bfcccd
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10/261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: "npm:^1.0.0"
  checksum: 10/2d8c9ab8cebb572e3362f7d06139a4592105983d4317e68f7adba320fe6ddfc8874581e0971e899e633fd5f72e262830edce36d5a0bc863dad17ad20572484b2
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: "npm:^1.0.0"
  checksum: 10/7eaed07728eaa28b77fadccabce53f30de467ff186a766872669a833ac2e87d8922b76a22cc58339d7e0277aefe98d6d00762113b27a97cdf65adcf958970935
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10/959385c98696ebbca51e7534e0dc723ada325efa3475350951363cce216d27373e0259b63edb599f72eb94d6cde8577b4b2375f080b303947e560f85692834fa
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10/c74c5f5ceee3c8a5b8bc37719840dc3749f5b0306d818974141dda2471a1a2ca6c8e46b9d6ac222c5345df7a901c9b6f350b1e6d62763fec877e26609a401bfe
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10/7898a9c1788b2862cf0f9c345a6bec77ba4a0c0983c7f19d610c382343d4f98fa260686b225dfb1f88393a66679d2ec58ee310c1d6868c081eda7918f32cc70a
  languageName: node
  linkType: hard

"hast-util-to-jsx-runtime@npm:^2.0.0":
  version: 2.3.6
  resolution: "hast-util-to-jsx-runtime@npm:2.3.6"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    "@types/hast": "npm:^3.0.0"
    "@types/unist": "npm:^3.0.0"
    comma-separated-tokens: "npm:^2.0.0"
    devlop: "npm:^1.0.0"
    estree-util-is-identifier-name: "npm:^3.0.0"
    hast-util-whitespace: "npm:^3.0.0"
    mdast-util-mdx-expression: "npm:^2.0.0"
    mdast-util-mdx-jsx: "npm:^3.0.0"
    mdast-util-mdxjs-esm: "npm:^2.0.0"
    property-information: "npm:^7.0.0"
    space-separated-tokens: "npm:^2.0.0"
    style-to-js: "npm:^1.0.0"
    unist-util-position: "npm:^5.0.0"
    vfile-message: "npm:^4.0.0"
  checksum: 10/111bd69f482952c7591cb4e1d3face25f1c18849b310a4d6cacc91e2d2cbc965d455fad35c059b8f0cfd762e933b826a7090b6f3098dece08307a6569de8f1d8
  languageName: node
  linkType: hard

"hast-util-whitespace@npm:^3.0.0":
  version: 3.0.0
  resolution: "hast-util-whitespace@npm:3.0.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
  checksum: 10/8c7e9eeb8131fc18702f3a42623eb6b0b09d470347aa8badacac70e6d91f79657ab8c6b57c4c6fee3658cff405fac30e816d1cdfb3ed1fbf6045d0a4555cf4d4
  languageName: node
  linkType: hard

"hi-base32@npm:^0.5.0":
  version: 0.5.1
  resolution: "hi-base32@npm:0.5.1"
  checksum: 10/f2cd52dca0f73d04786b98401aee2e27c58d015cc040eb35922c4231a06ca9e0cb79ce0a1556f18acf0f33233dbd52f7106fc950a38fdd65e5cb5758e4fcd9a1
  languageName: node
  linkType: hard

"html-url-attributes@npm:^3.0.0":
  version: 3.0.1
  resolution: "html-url-attributes@npm:3.0.1"
  checksum: 10/494074c2f730c5c0e517aa1b10111fb36732534a2d2b70427582c4a615472b47da472cf3a17562cc653826d378d20960f2783e0400f4f7cf0c3c2d91c6188d13
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10/4efd2dfcfeea9d5e88c84af450b9980be8a43c2c8179508b1c57c7b4421c855f3e8efe92fa53e0b3f4a43c85824ada930eabbc306d1b3beab750b6dcc5187693
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10/d062acfa0cb82beeb558f1043c6ba770ea892b5fb7b28654dbc70ea2aeea55226dd34c02a294f6c1ca179a5aa483c4ea641846821b182edbd9cc5d89b54c6848
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10/784b628cbd55b25542a9d85033bdfd03d4eda630fb8b3c9477959367f3be95dc476ed2ecbb9836c359c7c698027fc7b45723a302324433590f45d6c1706e8c13
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10/24e3292dd3dadaa81d065c6f8c41b274a47098150d444b96e5f53b4638a9a71482921ea6a91a1f59bb71d9796de25e04afd05919fa64c360347ba65d3766f10f
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0, ignore@npm:^5.3.1":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 10/cceb6a457000f8f6a50e1196429750d782afce5680dd878aa4221bd79972d68b3a55b4b1458fc682be978f4d3c6a249046aa0880637367216444ab7b014cfc98
  languageName: node
  linkType: hard

"import-fresh@npm:^3.0.0, import-fresh@npm:^3.2.1":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: "npm:^1.0.0"
    resolve-from: "npm:^4.0.0"
  checksum: 10/a06b19461b4879cc654d46f8a6244eb55eb053437afd4cbb6613cad6be203811849ed3e4ea038783092879487299fda24af932b86bdfff67c9055ba3612b8c87
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10/2d30b157a91fe1c1d7c6f653cbf263f039be6c5bfa959245a16d4ee191fc0f2af86c08545b6e6beeb041c56b574d2d5b9f95343d378ab49c0f37394d541e7fc8
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10/d2ebd65441a38c8336c223d1b80b921b9fa737e37ea466fd7e253cb000c64ae1f17fa59e68130ef5bda92cfd8d36b83d37dab0eb0a4558bcfec8e8cdfd2dcb67
  languageName: node
  linkType: hard

"inherits@npm:2":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10/cd45e923bee15186c07fa4c89db0aace24824c482fb887b528304694b2aa6ff8a898da8657046a5dcf3e46cd6db6c61629551f9215f208d7c3f157cf9b290521
  languageName: node
  linkType: hard

"inline-style-parser@npm:0.2.4":
  version: 0.2.4
  resolution: "inline-style-parser@npm:0.2.4"
  checksum: 10/80814479d1f3c9cbd102f9de4cd6558cf43cc2e48640e81c4371c3634f1e8b6dfeb2f21063cfa31d46cc83e834c20cd59ed9eeed9bfd45ef5bc02187ad941faf
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    hasown: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10/1d5219273a3dab61b165eddf358815eefc463207db33c20fcfca54717da02e3f492003757721f972fd0bf21e4b426cab389c5427b99ceea4b8b670dc88ee6d4a
  languageName: node
  linkType: hard

"intl-messageformat@npm:^10.1.0":
  version: 10.7.16
  resolution: "intl-messageformat@npm:10.7.16"
  dependencies:
    "@formatjs/ecma402-abstract": "npm:2.3.4"
    "@formatjs/fast-memoize": "npm:2.2.7"
    "@formatjs/icu-messageformat-parser": "npm:2.11.2"
    tslib: "npm:^2.8.0"
  checksum: 10/c19b77c5e495ce8b0d1aa0d95444bf3a4f73886805f1e08d7159b364abcf2f63686b2ccf202eaafb0e39a0e9fde61848b8dd2db1679efd4f6ec8f6a3d0e77928
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10/1ed81e06721af012306329b31f532b5e24e00cb537be18ddc905a84f19fe8f83a09a1699862bf3a1ec4b9dea93c55a3fa5faf8b5ea380431469df540f38b092c
  languageName: node
  linkType: hard

"is-alphabetical@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-alphabetical@npm:2.0.1"
  checksum: 10/56207db8d9de0850f0cd30f4966bf731eb82cedfe496cbc2e97e7c3bacaf66fc54a972d2d08c0d93bb679cb84976a05d24c5ad63de56fabbfc60aadae312edaa
  languageName: node
  linkType: hard

"is-alphanumerical@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-alphanumerical@npm:2.0.1"
  dependencies:
    is-alphabetical: "npm:^2.0.0"
    is-decimal: "npm:^2.0.0"
  checksum: 10/87acc068008d4c9c4e9f5bd5e251041d42e7a50995c77b1499cf6ed248f971aadeddb11f239cabf09f7975ee58cac7a48ffc170b7890076d8d227b24a68663c9
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10/ef1095c55b963cd0dcf6f88a113e44a0aeca91e30d767c475e7d746d28d1195b10c5076b94491a7a0cd85020ca6a4923070021d74651d093dc909e9932cf689b
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 10/81a78d518ebd8b834523e25d102684ee0f7e98637136d3bdc93fd09636350fa06f1d8ca997ea28143d4d13cb1b69c0824f082db0ac13e1ab3311c10ffea60ade
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: "npm:^1.0.0"
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.1"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10/7c2ac7efdf671e03265e74a043bcb1c0a32e226bc2a42dfc5ec8644667df668bbe14b91c08e6c1414f392f8cf86cd1d489b3af97756e2c7a49dd1ba63fd40ca6
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: "npm:^1.0.2"
  checksum: 10/10cf327310d712fe227cfaa32d8b11814c214392b6ac18c827f157e1e85363cf9c8e2a22df526689bd5d25e53b58cc110894787afb54e138e7c504174dba15fd
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10/078e51b4f956c2c5fd2b26bb2672c3ccf7e1faff38e0ebdba45612265f4e3d9fc3127a1fa8370bbf09eab61339203c3d3b7af5662cbf8be4030f8fac37745b0e
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/051fa95fdb99d7fbf653165a7e6b2cba5d2eb62f7ffa81e793a790f3fb5366c91c1b7b6af6820aa2937dd86c73aa3ca9d9ca98f500988457b1c59692c52ba911
  languageName: node
  linkType: hard

"is-bun-module@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-bun-module@npm:2.0.0"
  dependencies:
    semver: "npm:^7.7.1"
  checksum: 10/cded5a1a58368b847872d08617975d620ad94426d76a932f3e08d55b4574d199e0a62a4fb024fa2dc444200b71719eb0bffc5d3d1e1cc82e29b293bb8d66a990
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 10/48a9297fb92c99e9df48706241a189da362bff3003354aea4048bd5f7b2eb0d823cd16d0a383cece3d76166ba16d85d9659165ac6fcce1ac12e6c649d66dbdb9
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0, is-core-module@npm:^2.15.1, is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10/452b2c2fb7f889cbbf7e54609ef92cf6c24637c568acc7e63d166812a0fb365ae8a504c333a29add8bdb1686704068caa7f4e4b639b650dde4f00a038b8941fb
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    is-typed-array: "npm:^1.1.13"
  checksum: 10/357e9a48fa38f369fd6c4c3b632a3ab2b8adca14997db2e4b3fe94c4cd0a709af48e0fb61b02c64a90c0dd542fd489d49c2d03157b05ae6c07f5e4dec9e730a8
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/3a811b2c3176fb31abee1d23d3dc78b6c65fd9c07d591fcb67553cab9e7f272728c3dd077d2d738b53f9a2103255b0a6e8dfc9568a7805c56a78b2563e8d1dec
  languageName: node
  linkType: hard

"is-decimal@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-decimal@npm:2.0.1"
  checksum: 10/97132de7acdce77caa7b797632970a2ecd649a88e715db0e4dbc00ab0708b5e7574ba5903962c860cd4894a14fd12b100c0c4ac8aed445cf6f55c6cf747a4158
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10/df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10/0bfb145e9a1ba852ddde423b0926d2169ae5fe9e37882cde9e8f69031281a986308df4d982283e152396e88b86562ed2256cbaa5e6390fb840a4c25ab54b8a80
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10/44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-proto: "npm:^1.0.0"
    has-tostringtag: "npm:^1.0.2"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10/5906ff51a856a5fbc6b90a90fce32040b0a6870da905f98818f1350f9acadfc9884f7c3dec833fce04b83dd883937b86a190b6593ede82e8b1af8b6c4ecf7cbd
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10/3ed74f2b0cdf4f401f38edb0442ddfde3092d79d7d35c9919c86641efdbcbb32e45aa3c0f70ce5eecc946896cd5a0f26e4188b9f2b881876f7cb6c505b82da11
  languageName: node
  linkType: hard

"is-hexadecimal@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-hexadecimal@npm:2.0.1"
  checksum: 10/66a2ea85994c622858f063f23eda506db29d92b52580709eb6f4c19550552d4dcf3fb81952e52f7cf972097237959e00adc7bb8c9400cd12886e15bf06145321
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: 10/8de7b41715b08bcb0e5edb0fb9384b80d2d5bcd10e142188f33247d19ff078abaf8e9b6f858e2302d8d05376a26a55cd23a3c9f8ab93292b02fcd2cc9e4e92bb
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/a5922fb8779ab1ea3b8a9c144522b3d0bea5d9f8f23f7a72470e61e1e4df47714e28e0154ac011998b709cce260c3c9447ad3cd24a96c2f2a0abfdb2cbdc76c8
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10/6a6c3383f68afa1e05b286af866017c78f1226d43ac8cb064e115ff9ed85eb33f5c4f7216c96a71e4dfea289ef52c5da3aef5bbfade8ffe47a0465d70c0c8e86
  languageName: node
  linkType: hard

"is-plain-obj@npm:^4.0.0":
  version: 4.1.0
  resolution: "is-plain-obj@npm:4.1.0"
  checksum: 10/6dc45da70d04a81f35c9310971e78a6a3c7a63547ef782e3a07ee3674695081b6ca4e977fbb8efc48dae3375e0b34558d2bcd722aec9bddfa2d7db5b041be8ce
  languageName: node
  linkType: hard

"is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10/c42b7efc5868a5c9a4d8e6d3e9816e8815c611b09535c00fead18a1138455c5cb5e1887f0023a467ad3f9c419d62ba4dc3d9ba8bafe55053914d6d6454a945d2
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 10/5685df33f0a4a6098a98c72d94d67cad81b2bc72f1fb2091f3d9283c4a1c582123cd709145b02a9745f0ce6b41e3e43f1c944496d1d74d4ea43358be61308669
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10/0380d7c60cc692856871526ffcd38a8133818a2ee42d47bb8008248a0cd2121d8c8b5f66b6da3cac24bc5784553cacb6faaf678f66bc88c6615b42af2825230e
  languageName: node
  linkType: hard

"is-string@npm:^1.0.7, is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/5277cb9e225a7cc8a368a72623b44a99f2cfa139659c6b203553540681ad4276bfc078420767aad0e73eef5f0bd07d4abf39a35d37ec216917879d11cebc1f8b
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    safe-regex-test: "npm:^1.1.0"
  checksum: 10/db495c0d8cd0a7a66b4f4ef7fccee3ab5bd954cb63396e8ac4d32efe0e9b12fdfceb851d6c501216a71f4f21e5ff20fc2ee845a3d52d455e021c466ac5eb2db2
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: "npm:^1.1.16"
  checksum: 10/e8cf60b9ea85667097a6ad68c209c9722cfe8c8edf04d6218366469e51944c5cc25bae45ffb845c23f811d262e4314d3b0168748eb16711aa34d12724cdf0735
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: 10/a7b7e23206c542dcf2fa0abc483142731788771527e90e7e24f658c0833a0d91948a4f7b30d78f7a65255a48512e41a0288b778ba7fc396137515c12e201fd11
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: "npm:^1.0.3"
  checksum: 10/543506fd8259038b371bb083aac25b16cb4fd8b12fc58053aa3d45ac28dfd001cd5c6dffbba7aeea4213c74732d46b6cb2cfb5b412eed11f2db524f3f97d09a0
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: "npm:^1.0.3"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10/1d5e1d0179beeed3661125a6faa2e59bfb48afda06fc70db807f178aa0ebebc3758fb6358d76b3d528090d5ef85148c345dcfbf90839592fe293e3e5e82f2134
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: 10/1d8bc7911e13bb9f105b1b3e0b396c787a9e63046af0b8fe0ab1414488ab06b2b099b87a2d8a9e31d21c9a6fad773c7fc8b257c4880f2d957274479d28ca3414
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10/7c9f715c03aff08f35e98b1fadae1b9267b38f0615d501824f9743f3aab99ef10e303ce7db3f186763a0b70a19de5791ebfc854ff884d5a8c4d92211f642ec92
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10/7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"iterator.prototype@npm:^1.1.4":
  version: 1.1.5
  resolution: "iterator.prototype@npm:1.1.5"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.6"
    get-proto: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10/352bcf333f42189e65cc8cb2dcb94a5c47cf0a9110ce12aba788d405a980b5f5f3a06c79bf915377e1d480647169babd842ded0d898bed181bf6686e8e6823f6
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10/96f8786eaab98e4bf5b2a5d6d9588ea46c4d06bbc4f2eb861fdd7b6b182b16f71d8a70e79820f335d52653b16d4843b29dd9cdcf38ae80406756db9199497cf3
  languageName: node
  linkType: hard

"jest-worker@npm:^27.4.5":
  version: 27.5.1
  resolution: "jest-worker@npm:27.5.1"
  dependencies:
    "@types/node": "npm:*"
    merge-stream: "npm:^2.0.0"
    supports-color: "npm:^8.0.0"
  checksum: 10/06c6e2a84591d9ede704d5022fc13791e8876e83397c89d481b0063332abbb64c0f01ef4ca7de520b35c7a1058556078d6bdc3631376f4e9ffb42316c1a8488e
  languageName: node
  linkType: hard

"jiti@npm:^1.21.6":
  version: 1.21.7
  resolution: "jiti@npm:1.21.7"
  bin:
    jiti: bin/jiti.js
  checksum: 10/6a182521532126e4b7b5ad64b64fb2e162718fc03bc6019c21aa2222aacde6c6dfce4fc3bce9f69561a73b24ab5f79750ad353c37c3487a220d5869a39eae3a2
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10/af37d0d913fb56aec6dc0074c163cc71cd23c0b8aad5c2350747b6721d37ba118af35abdd8b33c47ec2800de07dedb16a527ca9c530ee004093e04958bd0cbf2
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10/c138a34a3fd0d08ebaf71273ad4465569a483b8a639e0b118ff65698d257c2791d3199e3f303631f2cb98213fa7b5f5d6a4621fd0fff819421b990d30d967140
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10/bebe7ae829bbd586ce8cbe83501dd8cb8c282c8902a8aeeed0a073a89dc37e8103b1244f3c6acd60278bcbfe12d93a3f83c9ac396868a3b3bbc3c5e5e3b648ef
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10/20bd37a142eca5d1794f354db8f1c9aeb54d85e1f5c247b371de05d23a9751ecd7bd3a9c4fc5298ea6fa09a100dafb4190fa5c98c6610b75952c3487f3ce7967
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 10/82876154521b7b68ba71c4f969b91572d1beabadd87bd3a6b236f85fbc7dc4695089191ed60bb59f9340993c51b33d479f45b6ba9f3548beb519705281c32c3c
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.1":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 10/5f3a99009ed5f2a5a67d06e2f298cc97bc86d462034173308156f15b43a6e850be8511dc204b9b94566305da2947f7d90289657237d210351a39059ff9d666cf
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10/7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 10/02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: 10/12786c2e2f22c27439e6db0532ba321f1d0617c27ad8cb1c352a0e9249a50182fd1ba8b52a18899291604b0c32eafa8afd09e51203f19109a0537f68db2b652d
  languageName: node
  linkType: hard

"json5@npm:^1.0.2":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: "npm:^1.2.0"
  bin:
    json5: lib/cli.js
  checksum: 10/a78d812dbbd5642c4f637dd130954acfd231b074965871c3e28a5bbd571f099d623ecf9161f1960c4ddf68e0cc98dee8bebfdb94a71ad4551f85a1afc94b63f6
  languageName: node
  linkType: hard

"json5@npm:^2.1.2, json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10/1db67b853ff0de3534085d630691d3247de53a2ed1390ba0ddff681ea43e9b3e30ecbdb65c5e9aab49435e44059c23dbd6fee8ee619419ba37465bb0dd7135da
  languageName: node
  linkType: hard

"jsx-ast-utils@npm:^2.4.1 || ^3.0.0, jsx-ast-utils@npm:^3.3.5":
  version: 3.3.5
  resolution: "jsx-ast-utils@npm:3.3.5"
  dependencies:
    array-includes: "npm:^3.1.6"
    array.prototype.flat: "npm:^1.3.1"
    object.assign: "npm:^4.1.4"
    object.values: "npm:^1.1.6"
  checksum: 10/b61d44613687dfe4cc8ad4b4fbf3711bf26c60b8d5ed1f494d723e0808415c59b24a7c0ed8ab10736a40ff84eef38cbbfb68b395e05d31117b44ffc59d31edfc
  languageName: node
  linkType: hard

"keyv@npm:^4.5.3":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: "npm:3.0.1"
  checksum: 10/167eb6ef64cc84b6fa0780ee50c9de456b422a1e18802209234f7c2cf7eae648c7741f32e50d7e24ccb22b24c13154070b01563d642755b156c357431a191e75
  languageName: node
  linkType: hard

"language-subtag-registry@npm:^0.3.20":
  version: 0.3.23
  resolution: "language-subtag-registry@npm:0.3.23"
  checksum: 10/fe13ed74ab9f862db8e5747b98cc9aa08d52a19f85b5cdb4975cd364c8539bd2da3380e4560d2dbbd728ec33dff8a4b4421fcb2e5b1b1bdaa21d16f91a54d0d4
  languageName: node
  linkType: hard

"language-tags@npm:^1.0.9":
  version: 1.0.9
  resolution: "language-tags@npm:1.0.9"
  dependencies:
    language-subtag-registry: "npm:^0.3.20"
  checksum: 10/d3a7c14b694e67f519153d6df6cb200681648d38d623c3bfa9d6a66a5ec5493628acb88e9df5aceef3cf1902ab263a205e7d59ee4cf1d6bb67e707b83538bd6d
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:~0.4.0"
  checksum: 10/2e4720ff79f21ae08d42374b0a5c2f664c5be8b6c8f565bb4e1315c96ed3a8acaa9de788ffed82d7f2378cf36958573de07ef92336cb5255ed74d08b8318c9ee
  languageName: node
  linkType: hard

"lilconfig@npm:^3.0.0, lilconfig@npm:^3.1.3":
  version: 3.1.3
  resolution: "lilconfig@npm:3.1.3"
  checksum: 10/b932ce1af94985f0efbe8896e57b1f814a48c8dbd7fc0ef8469785c6303ed29d0090af3ccad7e36b626bfca3a4dc56cc262697e9a8dd867623cf09a39d54e4c3
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 10/0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"loader-runner@npm:^4.2.0":
  version: 4.3.0
  resolution: "loader-runner@npm:4.3.0"
  checksum: 10/555ae002869c1e8942a0efd29a99b50a0ce6c3296efea95caf48f00d7f6f7f659203ed6613688b6181aa81dc76de3e65ece43094c6dffef3127fe1a84d973cd3
  languageName: node
  linkType: hard

"loader-utils@npm:^2.0.4":
  version: 2.0.4
  resolution: "loader-utils@npm:2.0.4"
  dependencies:
    big.js: "npm:^5.2.2"
    emojis-list: "npm:^3.0.0"
    json5: "npm:^2.1.2"
  checksum: 10/28bd9af2025b0cb2fc6c9c2d8140a75a3ab61016e5a86edf18f63732216e985a50bf2479a662555beb472a54d12292e380423705741bfd2b54cab883aa067f18
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: "npm:^4.1.0"
  checksum: 10/83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10/d0ea2dd0097e6201be083865d50c3fb54fbfbdb247d9cc5950e086c991f448b7ab0cdab0d57eacccb43473d3f2acd21e134db39f22dac2d6c9ba6bf26978e3d6
  languageName: node
  linkType: hard

"lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10/c08619c038846ea6ac754abd6dd29d2568aa705feb69339e836dfa8d8b09abbb2f859371e86863eda41848221f9af43714491467b5b0299122431e202bb0c532
  languageName: node
  linkType: hard

"longest-streak@npm:^3.0.0":
  version: 3.1.0
  resolution: "longest-streak@npm:3.1.0"
  checksum: 10/d7f952ed004cbdb5c8bcfc4f7f5c3d65449e6c5a9e9be4505a656e3df5a57ee125f284286b4bf8ecea0c21a7b3bf2b8f9001ad506c319b9815ad6a63a47d0fd0
  languageName: node
  linkType: hard

"loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: "npm:^3.0.0 || ^4.0.0"
  bin:
    loose-envify: cli.js
  checksum: 10/6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10/e6e90267360476720fa8e83cc168aa2bf0311f3f2eea20a6ba78b90a885ae72071d9db132f40fda4129c803e7dcec3a6b6a6fbb44ca90b081630b810b5d6a41a
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10/951d2673dcc64a7fb888bf3d13bc2fdf923faca97d89cdb405ba3dfff77e2b26e5798d405e78fcd7094c9e7b8b4dab2ddc5a4f8a11928af24a207b7c738ca3f8
  languageName: node
  linkType: hard

"make-dir@npm:^3.0.2, make-dir@npm:^3.1.0":
  version: 3.1.0
  resolution: "make-dir@npm:3.1.0"
  dependencies:
    semver: "npm:^6.0.0"
  checksum: 10/484200020ab5a1fdf12f393fe5f385fc8e4378824c940fba1729dcd198ae4ff24867bc7a5646331e50cead8abff5d9270c456314386e629acec6dff4b8016b78
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10/fce0385840b6d86b735053dfe941edc2dd6468fda80fe74da1eeff10cbd82a75760f406194f2bc2fa85b99545b2bc1f84c08ddf994b21830775ba2d1a87e8bdf
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10/11df2eda46d092a6035479632e1ec865b8134bdfc4bd9e571a656f4191525404f13a283a515938c3a8de934dbfd9c09674d9da9fa831e6eb7e22b50b197d2edd
  languageName: node
  linkType: hard

"mdast-util-from-markdown@npm:^2.0.0":
  version: 2.0.2
  resolution: "mdast-util-from-markdown@npm:2.0.2"
  dependencies:
    "@types/mdast": "npm:^4.0.0"
    "@types/unist": "npm:^3.0.0"
    decode-named-character-reference: "npm:^1.0.0"
    devlop: "npm:^1.0.0"
    mdast-util-to-string: "npm:^4.0.0"
    micromark: "npm:^4.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^2.0.0"
    micromark-util-decode-string: "npm:^2.0.0"
    micromark-util-normalize-identifier: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
    unist-util-stringify-position: "npm:^4.0.0"
  checksum: 10/69b207913fbcc0469f8c59d922af4d5509b79e809d77c9bd4781543a907fe2ecc8e6433ce0707066a27b117b13f38af3aae4f2d085e18ebd2d3ad5f1a5647902
  languageName: node
  linkType: hard

"mdast-util-mdx-expression@npm:^2.0.0":
  version: 2.0.1
  resolution: "mdast-util-mdx-expression@npm:2.0.1"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    devlop: "npm:^1.0.0"
    mdast-util-from-markdown: "npm:^2.0.0"
    mdast-util-to-markdown: "npm:^2.0.0"
  checksum: 10/70e860f8ee22c4f478449942750055d649d4380bf43b235d0710af510189d285fb057e401d20b59596d9789f4e270fce08ca892dc849676f9e3383b991d52485
  languageName: node
  linkType: hard

"mdast-util-mdx-jsx@npm:^3.0.0":
  version: 3.2.0
  resolution: "mdast-util-mdx-jsx@npm:3.2.0"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    "@types/unist": "npm:^3.0.0"
    ccount: "npm:^2.0.0"
    devlop: "npm:^1.1.0"
    mdast-util-from-markdown: "npm:^2.0.0"
    mdast-util-to-markdown: "npm:^2.0.0"
    parse-entities: "npm:^4.0.0"
    stringify-entities: "npm:^4.0.0"
    unist-util-stringify-position: "npm:^4.0.0"
    vfile-message: "npm:^4.0.0"
  checksum: 10/62cd650a522e5d72ea6afd6d4a557fc86525b802d097a29a2fbe17d22e7b97c502a580611873e4d685777fe77c6ff8d39fb6e37d026b3acbc86c3b24927f4ad9
  languageName: node
  linkType: hard

"mdast-util-mdxjs-esm@npm:^2.0.0":
  version: 2.0.1
  resolution: "mdast-util-mdxjs-esm@npm:2.0.1"
  dependencies:
    "@types/estree-jsx": "npm:^1.0.0"
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    devlop: "npm:^1.0.0"
    mdast-util-from-markdown: "npm:^2.0.0"
    mdast-util-to-markdown: "npm:^2.0.0"
  checksum: 10/05474226e163a3f407fccb5780b0d8585a95e548e5da4a85227df43f281b940c7941a9a9d4af1be4f885fe554731647addb057a728e87aa1f503ff9cc72c9163
  languageName: node
  linkType: hard

"mdast-util-phrasing@npm:^4.0.0":
  version: 4.1.0
  resolution: "mdast-util-phrasing@npm:4.1.0"
  dependencies:
    "@types/mdast": "npm:^4.0.0"
    unist-util-is: "npm:^6.0.0"
  checksum: 10/3a97533e8ad104a422f8bebb34b3dde4f17167b8ed3a721cf9263c7416bd3447d2364e6d012a594aada40cac9e949db28a060bb71a982231693609034ed5324e
  languageName: node
  linkType: hard

"mdast-util-to-hast@npm:^13.0.0":
  version: 13.2.0
  resolution: "mdast-util-to-hast@npm:13.2.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    "@ungap/structured-clone": "npm:^1.0.0"
    devlop: "npm:^1.0.0"
    micromark-util-sanitize-uri: "npm:^2.0.0"
    trim-lines: "npm:^3.0.0"
    unist-util-position: "npm:^5.0.0"
    unist-util-visit: "npm:^5.0.0"
    vfile: "npm:^6.0.0"
  checksum: 10/b17ee338f843af31a1c7a2ebf0df6f0b41c9380b7119a63ab521d271df665456578e1234bb7617883e8d860fe878038dcf2b76ab2f21e0f7451215a096d26cce
  languageName: node
  linkType: hard

"mdast-util-to-markdown@npm:^2.0.0":
  version: 2.1.2
  resolution: "mdast-util-to-markdown@npm:2.1.2"
  dependencies:
    "@types/mdast": "npm:^4.0.0"
    "@types/unist": "npm:^3.0.0"
    longest-streak: "npm:^3.0.0"
    mdast-util-phrasing: "npm:^4.0.0"
    mdast-util-to-string: "npm:^4.0.0"
    micromark-util-classify-character: "npm:^2.0.0"
    micromark-util-decode-string: "npm:^2.0.0"
    unist-util-visit: "npm:^5.0.0"
    zwitch: "npm:^2.0.0"
  checksum: 10/ab494a32f1ec90f0a502970b403b1847a10f3ba635adddb66ce70994cc47b4924c6c05078ddd29a8c2c5c9bc8c0bcc20e5fc1ef0fcb9b0cb9c0589a000817f1c
  languageName: node
  linkType: hard

"mdast-util-to-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "mdast-util-to-string@npm:4.0.0"
  dependencies:
    "@types/mdast": "npm:^4.0.0"
  checksum: 10/f4a5dbb9ea03521d7d3e26a9ba5652a1d6fbd55706dddd2155427517085688830e0ecd3f12418cfd40892640886eb39a4034c3c967d85e01e2fa64cfb53cff05
  languageName: node
  linkType: hard

"medusa-next@workspace:.":
  version: 0.0.0-use.local
  resolution: "medusa-next@workspace:."
  dependencies:
    "@babel/core": "npm:^7.17.5"
    "@headlessui/react": "npm:^2.2.0"
    "@hookform/resolvers": "npm:^3.9.0"
    "@medusajs/icons": "npm:latest"
    "@medusajs/js-sdk": "npm:latest"
    "@medusajs/types": "npm:latest"
    "@medusajs/ui": "npm:latest"
    "@medusajs/ui-preset": "npm:latest"
    "@paypal/paypal-js": "npm:^5.0.6"
    "@paypal/react-paypal-js": "npm:^7.8.1"
    "@radix-ui/react-dialog": "npm:^1.1.1"
    "@stripe/react-stripe-js": "npm:^1.7.2"
    "@stripe/stripe-js": "npm:^1.29.0"
    "@types/lodash": "npm:^4.14.195"
    "@types/node": "npm:17.0.21"
    "@types/pg": "npm:^8.11.0"
    "@types/react": "npm:types-react@19.0.0-rc.1"
    "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"
    "@types/wicg-file-system-access": "npm:^2023.10.5"
    "@vercel/analytics": "npm:^1.4.1"
    ansi-colors: "npm:^4.1.3"
    autoprefixer: "npm:^10.4.2"
    babel-loader: "npm:^8.2.3"
    eslint: "npm:8.10.0"
    eslint-config-next: "npm:15.0.1"
    geist: "npm:^1.3.1"
    lodash: "npm:^4.17.21"
    next: "npm:^15.2.4"
    pg: "npm:^8.11.3"
    postcss: "npm:^8.4.8"
    prettier: "npm:^2.8.8"
    react: "npm:^19.1.0"
    react-country-flag: "npm:^3.0.2"
    react-dom: "npm:^19.1.0"
    react-hook-form: "npm:^7.53.0"
    react-intersection-observer: "npm:^9.3.4"
    react-markdown: "npm:^9.0.1"
    server-only: "npm:^0.0.1"
    tailwindcss: "npm:^3.4.1"
    typescript: "npm:^5.5.3"
    webpack: "npm:^5"
    zod: "npm:3.22.4"
  languageName: unknown
  linkType: soft

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 10/6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10/7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"micromark-core-commonmark@npm:^2.0.0":
  version: 2.0.3
  resolution: "micromark-core-commonmark@npm:2.0.3"
  dependencies:
    decode-named-character-reference: "npm:^1.0.0"
    devlop: "npm:^1.0.0"
    micromark-factory-destination: "npm:^2.0.0"
    micromark-factory-label: "npm:^2.0.0"
    micromark-factory-space: "npm:^2.0.0"
    micromark-factory-title: "npm:^2.0.0"
    micromark-factory-whitespace: "npm:^2.0.0"
    micromark-util-character: "npm:^2.0.0"
    micromark-util-chunked: "npm:^2.0.0"
    micromark-util-classify-character: "npm:^2.0.0"
    micromark-util-html-tag-name: "npm:^2.0.0"
    micromark-util-normalize-identifier: "npm:^2.0.0"
    micromark-util-resolve-all: "npm:^2.0.0"
    micromark-util-subtokenize: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10/2b98b9eba1463850ebd8f338f966bd2113dafe764b490ebee3dccab3764d3c48b53fe67673297530e56bf54f58de27dfd1952ed79c5b4e32047cb7f29bd807f2
  languageName: node
  linkType: hard

"micromark-factory-destination@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-destination@npm:2.0.1"
  dependencies:
    micromark-util-character: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10/9c4baa9ca2ed43c061bbf40ddd3d85154c2a0f1f485de9dea41d7dd2ad994ebb02034a003b2c1dbe228ba83a0576d591f0e90e0bf978713f84ee7d7f3aa98320
  languageName: node
  linkType: hard

"micromark-factory-label@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-label@npm:2.0.1"
  dependencies:
    devlop: "npm:^1.0.0"
    micromark-util-character: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10/bd03f5a75f27cdbf03b894ddc5c4480fc0763061fecf9eb927d6429233c930394f223969a99472df142d570c831236134de3dc23245d23d9f046f9d0b623b5c2
  languageName: node
  linkType: hard

"micromark-factory-space@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-space@npm:2.0.1"
  dependencies:
    micromark-util-character: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10/1bd68a017c1a66f4787506660c1e1c5019169aac3b1cb075d49ac5e360e0b2065e984d4e1d6e9e52a9d44000f2fa1c98e66a743d7aae78b4b05616bf3242ed71
  languageName: node
  linkType: hard

"micromark-factory-title@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-title@npm:2.0.1"
  dependencies:
    micromark-factory-space: "npm:^2.0.0"
    micromark-util-character: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10/b4d2e4850a8ba0dff25ce54e55a3eb0d43dda88a16293f53953153288f9d84bcdfa8ca4606b2cfbb4f132ea79587bbb478a73092a349f893f5264fbcdbce2ee1
  languageName: node
  linkType: hard

"micromark-factory-whitespace@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-factory-whitespace@npm:2.0.1"
  dependencies:
    micromark-factory-space: "npm:^2.0.0"
    micromark-util-character: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10/67b3944d012a42fee9e10e99178254a04d48af762b54c10a50fcab988688799993efb038daf9f5dbc04001a97b9c1b673fc6f00e6a56997877ab25449f0c8650
  languageName: node
  linkType: hard

"micromark-util-character@npm:^2.0.0":
  version: 2.1.1
  resolution: "micromark-util-character@npm:2.1.1"
  dependencies:
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10/85da8f8e5f7ed16046575bef5b0964ca3fca3162b87b74ae279f1e48eb7160891313eb64f04606baed81c58b514dbdb64f1a9d110a51baaaa79225d72a7b1852
  languageName: node
  linkType: hard

"micromark-util-chunked@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-chunked@npm:2.0.1"
  dependencies:
    micromark-util-symbol: "npm:^2.0.0"
  checksum: 10/f8cb2a67bcefe4bd2846d838c97b777101f0043b9f1de4f69baf3e26bb1f9885948444e3c3aec66db7595cad8173bd4567a000eb933576c233d54631f6323fe4
  languageName: node
  linkType: hard

"micromark-util-classify-character@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-classify-character@npm:2.0.1"
  dependencies:
    micromark-util-character: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10/4d8bbe3a6dbf69ac0fc43516866b5bab019fe3f4568edc525d4feaaaf78423fa54e6b6732b5bccbeed924455279a3758ffc9556954aafb903982598a95a02704
  languageName: node
  linkType: hard

"micromark-util-combine-extensions@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-combine-extensions@npm:2.0.1"
  dependencies:
    micromark-util-chunked: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10/5d22fb9ee37e8143adfe128a72b50fa09568c2cc553b3c76160486c96dbbb298c5802a177a10a215144a604b381796071b5d35be1f2c2b2ee17995eda92f0c8e
  languageName: node
  linkType: hard

"micromark-util-decode-numeric-character-reference@npm:^2.0.0":
  version: 2.0.2
  resolution: "micromark-util-decode-numeric-character-reference@npm:2.0.2"
  dependencies:
    micromark-util-symbol: "npm:^2.0.0"
  checksum: 10/ee11c8bde51e250e302050474c4a2adca094bca05c69f6cdd241af12df285c48c88d19ee6e022b9728281c280be16328904adca994605680c43af56019f4b0b6
  languageName: node
  linkType: hard

"micromark-util-decode-string@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-decode-string@npm:2.0.1"
  dependencies:
    decode-named-character-reference: "npm:^1.0.0"
    micromark-util-character: "npm:^2.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
  checksum: 10/2f517e4c613609445db4b9a17f8c77832f55fb341620a8fd598f083c1227027485d601c2021c2f8f9883210b8671e7b3990f0c6feeecd49a136475465808c380
  languageName: node
  linkType: hard

"micromark-util-encode@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-encode@npm:2.0.1"
  checksum: 10/be890b98e78dd0cdd953a313f4148c4692cc2fb05533e56fef5f421287d3c08feee38ca679f318e740530791fc251bfe8c80efa926fcceb4419b269c9343d226
  languageName: node
  linkType: hard

"micromark-util-html-tag-name@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-html-tag-name@npm:2.0.1"
  checksum: 10/dea365f5ad28ad74ff29fcb581f7b74fc1f80271c5141b3b2bc91c454cbb6dfca753f28ae03730d657874fcbd89d0494d0e3965dfdca06d9855f467c576afa9d
  languageName: node
  linkType: hard

"micromark-util-normalize-identifier@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-normalize-identifier@npm:2.0.1"
  dependencies:
    micromark-util-symbol: "npm:^2.0.0"
  checksum: 10/1eb9a289d7da067323df9fdc78bfa90ca3207ad8fd893ca02f3133e973adcb3743b233393d23d95c84ccaf5d220ae7f5a28402a644f135dcd4b8cfa60a7b5f84
  languageName: node
  linkType: hard

"micromark-util-resolve-all@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-resolve-all@npm:2.0.1"
  dependencies:
    micromark-util-types: "npm:^2.0.0"
  checksum: 10/9275f3ddb6c26f254dd2158e66215d050454b279707a7d9ce5a3cd0eba23201021cedcb78ae1a746c1b23227dcc418ee40dd074ade195359506797a5493550cc
  languageName: node
  linkType: hard

"micromark-util-sanitize-uri@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-sanitize-uri@npm:2.0.1"
  dependencies:
    micromark-util-character: "npm:^2.0.0"
    micromark-util-encode: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
  checksum: 10/064c72abfc9777864ca0521a016dde62ab3e7af5215d10fd27e820798500d5d305da638459c589275c1a093cf588f493cc2f65273deac5a5331ecefc6c9ea78a
  languageName: node
  linkType: hard

"micromark-util-subtokenize@npm:^2.0.0":
  version: 2.1.0
  resolution: "micromark-util-subtokenize@npm:2.1.0"
  dependencies:
    devlop: "npm:^1.0.0"
    micromark-util-chunked: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10/5f18c70cb952a414a4d161f5d6a5254d33c7dfcd56577e592ef2e172a0414058d3531a3554f43538f14e243592fffbc2e68ddaf6a41c54577b3ba7beb555d3dc
  languageName: node
  linkType: hard

"micromark-util-symbol@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-symbol@npm:2.0.1"
  checksum: 10/497e6d95fc21c2bb5265b78a6a60db518c376dc438739b2e7d4aee6f9f165222711724b456c63163314f32b8eea68a064687711d41e986262926eab23ddb9229
  languageName: node
  linkType: hard

"micromark-util-types@npm:^2.0.0":
  version: 2.0.2
  resolution: "micromark-util-types@npm:2.0.2"
  checksum: 10/a9eb067bd9384eab61942285d53738aa22f3fef4819eaf20249bec6ec13f1e4da2800230fd0ceb7e705108987aa9062fe3e9a8e5e48aa60180db80b9489dc3e2
  languageName: node
  linkType: hard

"micromark@npm:^4.0.0":
  version: 4.0.2
  resolution: "micromark@npm:4.0.2"
  dependencies:
    "@types/debug": "npm:^4.0.0"
    debug: "npm:^4.0.0"
    decode-named-character-reference: "npm:^1.0.0"
    devlop: "npm:^1.0.0"
    micromark-core-commonmark: "npm:^2.0.0"
    micromark-factory-space: "npm:^2.0.0"
    micromark-util-character: "npm:^2.0.0"
    micromark-util-chunked: "npm:^2.0.0"
    micromark-util-combine-extensions: "npm:^2.0.0"
    micromark-util-decode-numeric-character-reference: "npm:^2.0.0"
    micromark-util-encode: "npm:^2.0.0"
    micromark-util-normalize-identifier: "npm:^2.0.0"
    micromark-util-resolve-all: "npm:^2.0.0"
    micromark-util-sanitize-uri: "npm:^2.0.0"
    micromark-util-subtokenize: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10/1b85e49c8f71013df2d07a59e477deb72cd325d41cc15f35b2aa52b8b7a93fed45498ce3e18ed34464a9afa9ba8a9210b2509454b2a2d16ac06c7429f562bfac
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10/6bf2a01672e7965eb9941d1f02044fad2bd12486b5553dc1116ff24c09a8723157601dc992e74c911d896175918448762df3b3fd0a6b61037dd1a9766ddfbf58
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10/54bb60bf39e6f8689f6622784e668a3d7f8bed6b0d886f5c3c446cb3284be28b30bf707ed05d0fe44a036f8469976b2629bbea182684977b084de9da274694d7
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.27":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10/89aa9651b67644035de2784a6e665fc685d79aba61857e02b9c8758da874a754aed4a9aced9265f5ed1171fd934331e5516b84a7f0218031b6fa0270eca1e51a
  languageName: node
  linkType: hard

"mini-svg-data-uri@npm:^1.2.3":
  version: 1.4.4
  resolution: "mini-svg-data-uri@npm:1.4.4"
  bin:
    mini-svg-data-uri: cli.js
  checksum: 10/1336c2b00b6a72b0ce3cf942f7ab074faf463b941042fbe51d7a70be119c5d4223880aaa29584d5a804496ca1dda9b6fff7dd5aa284721907519b646192d8aaa
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10/e0b25b04cd4ec6732830344e5739b13f8690f8a012d73445a4a19fbc623f5dd481ef7a5827fde25954cd6026fede7574cc54dc4643c99d6c6b653d6203f94634
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10/dd6a8927b063aca6d910b119e1f2df6d2ce7d36eab91de83167dd136bb85e1ebff97b0d3de1cb08bd1f7e018ca170b4962479fefab5b2a69e2ae12cb2edc8348
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10/908491b6cc15a6c440ba5b22780a0ba89b9810e1aea684e253e43c4e3b8d56ec1dcdd7ea96dde119c29df59c936cde16062159eae4225c691e19c70b432b6e6f
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10/7ddfebdbb87d9866e7b5f7eead5a9e3d9d507992af932a11d275551f60006cf7d9178e66d586dbb910894f3e3458d27c0ddf93c76e94d49d0a54a541ddc1263d
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10/40982d8d836a52b0f37049a0a7e5d0f089637298e6d9b45df9c115d4f0520682a78258905e5c8b180fb41b593b0a82cc1361d2c74b45f7ada66334f84d1ecfdd
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10/a5c6ef069f70d9a524d3428af39f2b117ff8cd84172e19b754e7264a33df460873e6eb3d6e55758531580970de50ae950c496256bb4ad3691a2974cddff189f0
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10/c25f0ee8196d8e6036661104bacd743785b2599a21de5c516b32b3fa2b83113ac89a2358465bc04956baab37ffb956ae43be679b2262bf7be15fce467ccd7950
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10/c075bed1594f68dcc8c35122333520112daefd4d070e5d0a228bd4cf5580e9eed3981b96c0ae1d62488e204e80fd27b2b9d0068ca9a5ef3993e9565faf63ca41
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10/16fd79c28645759505914561e249b9a1f5fe3362279ad95487a4501e4467abeb714fd35b95307326b8fd03f3c7719065ef11a6f97b7285d7888306d1bd2232ba
  languageName: node
  linkType: hard

"ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10/aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"mz@npm:^2.7.0":
  version: 2.7.0
  resolution: "mz@npm:2.7.0"
  dependencies:
    any-promise: "npm:^1.0.0"
    object-assign: "npm:^4.0.1"
    thenify-all: "npm:^1.0.0"
  checksum: 10/8427de0ece99a07e9faed3c0c6778820d7543e3776f9a84d22cf0ec0a8eb65f6e9aee9c9d353ff9a105ff62d33a9463c6ca638974cc652ee8140cd1e35951c87
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.6, nanoid@npm:^3.3.8":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10/73b5afe5975a307aaa3c95dfe3334c52cdf9ae71518176895229b8d65ab0d1c0417dd081426134eb7571c055720428ea5d57c645138161e7d10df80815527c48
  languageName: node
  linkType: hard

"napi-postinstall@npm:^0.2.2":
  version: 0.2.3
  resolution: "napi-postinstall@npm:0.2.3"
  bin:
    napi-postinstall: lib/cli.js
  checksum: 10/168525a8b80610c9f7dfe18feb40a86a6dc473d939c805eaa53a74bb1645ad9c3784904e41901f5f956f0147069d30a0b44e1bca5d73e94d8bbc112f36ae6eb7
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10/23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10/b5734e87295324fabf868e36fb97c84b7d7f3156ec5f4ee5bf6e488079c11054f818290fc33804cef7b1ee21f55eeb14caea83e7dafae6492a409b3e573153e5
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: 10/1a7948fea86f2b33ec766bc899c88796a51ba76a4afc9026764aedc6e7cde692a09067031e4a1bf6db4f978ccd99e7f5b6c03fe47ad9865c3d4f99050d67e002
  languageName: node
  linkType: hard

"next@npm:^15.2.4":
  version: 15.3.2
  resolution: "next@npm:15.3.2"
  dependencies:
    "@next/env": "npm:15.3.2"
    "@next/swc-darwin-arm64": "npm:15.3.2"
    "@next/swc-darwin-x64": "npm:15.3.2"
    "@next/swc-linux-arm64-gnu": "npm:15.3.2"
    "@next/swc-linux-arm64-musl": "npm:15.3.2"
    "@next/swc-linux-x64-gnu": "npm:15.3.2"
    "@next/swc-linux-x64-musl": "npm:15.3.2"
    "@next/swc-win32-arm64-msvc": "npm:15.3.2"
    "@next/swc-win32-x64-msvc": "npm:15.3.2"
    "@swc/counter": "npm:0.1.3"
    "@swc/helpers": "npm:0.5.15"
    busboy: "npm:1.6.0"
    caniuse-lite: "npm:^1.0.30001579"
    postcss: "npm:8.4.31"
    sharp: "npm:^0.34.1"
    styled-jsx: "npm:5.1.6"
  peerDependencies:
    "@opentelemetry/api": ^1.1.0
    "@playwright/test": ^1.41.2
    babel-plugin-react-compiler: "*"
    react: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
    react-dom: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
    sass: ^1.3.0
  dependenciesMeta:
    "@next/swc-darwin-arm64":
      optional: true
    "@next/swc-darwin-x64":
      optional: true
    "@next/swc-linux-arm64-gnu":
      optional: true
    "@next/swc-linux-arm64-musl":
      optional: true
    "@next/swc-linux-x64-gnu":
      optional: true
    "@next/swc-linux-x64-musl":
      optional: true
    "@next/swc-win32-arm64-msvc":
      optional: true
    "@next/swc-win32-x64-msvc":
      optional: true
    sharp:
      optional: true
  peerDependenciesMeta:
    "@opentelemetry/api":
      optional: true
    "@playwright/test":
      optional: true
    babel-plugin-react-compiler:
      optional: true
    sass:
      optional: true
  bin:
    next: dist/bin/next
  checksum: 10/cf58ce8d33fb58b5ba7ab6e5d9e8a18e45f4f8d96c4fbbf0eb19195dc78dc408b4879146e93a937a21cb9ef0b59dc5c5ae579db8d34464fe15f4a3638fd150b4
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10/806fd8e3adc9157e17bf0d4a2c899cf6b98a0bbe9f453f630094ce791866271f6cddcaf2133e6513715d934fcba2014d287c7053d5d7934937b3a34d5a3d84ad
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10/c2b33b4f0c40445aee56141f13ca692fa6805db88510e5bbb3baadb2da13e1293b738e638e15e4a8eb668bb9e97debb08e7a35409b477b5cc18f171d35a83045
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10/26ab456c51a96f02a9e5aa8d1b80ef3219f2070f3f3528a040e32fb735b1e651e17bdf0f1476988d3a46d498f35c65ed662d122f340d38ce4a7e71dd7b20c4bc
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10/88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"normalize-range@npm:^0.1.2":
  version: 0.1.2
  resolution: "normalize-range@npm:0.1.2"
  checksum: 10/9b2f14f093593f367a7a0834267c24f3cb3e887a2d9809c77d8a7e5fd08738bcd15af46f0ab01cc3a3d660386f015816b5c922cea8bf2ee79777f40874063184
  languageName: node
  linkType: hard

"object-assign@npm:^4.0.1, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10/fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-hash@npm:^3.0.0":
  version: 3.0.0
  resolution: "object-hash@npm:3.0.0"
  checksum: 10/f498d456a20512ba7be500cef4cf7b3c183cc72c65372a549c9a0e6dd78ce26f375e9b1315c07592d3fde8f10d5019986eba35970570d477ed9a2a702514432a
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10/aa13b1190ad3e366f6c83ad8a16ed37a19ed57d267385aa4bfdccda833d7b90465c057ff6c55d035a6b2e52c1a2295582b294217a0a3a1ae7abdd6877ef781fb
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: 10/3d81d02674115973df0b7117628ea4110d56042e5326413e4b4313f0bcdf7dd78d4a3acef2c831463fa3796a66762c49daef306f4a0ea1af44877d7086d73bde
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.4, object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
    has-symbols: "npm:^1.1.0"
    object-keys: "npm:^1.1.1"
  checksum: 10/3fe28cdd779f2a728a9a66bd688679ba231a2b16646cd1e46b528fe7c947494387dda4bc189eff3417f3717ef4f0a8f2439347cf9a9aa3cef722fbfd9f615587
  languageName: node
  linkType: hard

"object.entries@npm:^1.1.9":
  version: 1.1.9
  resolution: "object.entries@npm:1.1.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.1.1"
  checksum: 10/24163ab1e1e013796693fc5f5d349e8b3ac0b6a34a7edb6c17d3dd45c6a8854145780c57d302a82512c1582f63720f4b4779d6c1cfba12cbb1420b978802d8a3
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.8":
  version: 2.0.8
  resolution: "object.fromentries@npm:2.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/5b2e80f7af1778b885e3d06aeb335dcc86965e39464671adb7167ab06ac3b0f5dd2e637a90d8ebd7426d69c6f135a4753ba3dd7d0fe2a7030cf718dcb910fd92
  languageName: node
  linkType: hard

"object.groupby@npm:^1.0.3":
  version: 1.0.3
  resolution: "object.groupby@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.2"
  checksum: 10/44cb86dd2c660434be65f7585c54b62f0425b0c96b5c948d2756be253ef06737da7e68d7106e35506ce4a44d16aa85a413d11c5034eb7ce5579ec28752eb42d0
  languageName: node
  linkType: hard

"object.values@npm:^1.1.6, object.values@npm:^1.2.0, object.values@npm:^1.2.1":
  version: 1.2.1
  resolution: "object.values@npm:1.2.1"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/f5ec9eccdefeaaa834b089c525663436812a65ff13de7964a1c3a9110f32054f2d58aa476a645bb14f75a79f3fe1154fb3e7bfdae7ac1e80affe171b2ef74bce
  languageName: node
  linkType: hard

"obuf@npm:~1.1.2":
  version: 1.1.2
  resolution: "obuf@npm:1.1.2"
  checksum: 10/53ff4ab3a13cc33ba6c856cf281f2965c0aec9720967af450e8fd06cfd50aceeefc791986a16bcefa14e7898b3ca9acdfcf15b9d9a1b9c7e1366581a8ad6e65e
  languageName: node
  linkType: hard

"once@npm:^1.3.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10/cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"optionator@npm:^0.9.1":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: "npm:^0.1.3"
    fast-levenshtein: "npm:^2.0.6"
    levn: "npm:^0.4.1"
    prelude-ls: "npm:^1.2.1"
    type-check: "npm:^0.4.0"
    word-wrap: "npm:^1.2.5"
  checksum: 10/a8398559c60aef88d7f353a4f98dcdff6090a4e70f874c827302bf1213d9106a1c4d5fcb68dacb1feb3c30a04c4102f41047aa55d4c576b863d6fc876e001af6
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: "npm:^1.2.6"
    object-keys: "npm:^1.1.1"
    safe-push-apply: "npm:^1.0.0"
  checksum: 10/ab4bb3b8636908554fc19bf899e225444195092864cb61503a0d048fdaf662b04be2605b636a4ffeaf6e8811f6fcfa8cbb210ec964c0eb1a41eb853e1d5d2f41
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: "npm:^2.0.0"
  checksum: 10/84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: "npm:^2.2.0"
  checksum: 10/513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10/2ef48ccfc6dd387253d71bf502604f7893ed62090b2c9d73387f10006c342606b05233da0e4f29388227b61eb5aeface6197e166520c465c234552eeab2fe633
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: 10/f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10/58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: "npm:^3.0.0"
  checksum: 10/6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-entities@npm:^4.0.0":
  version: 4.0.2
  resolution: "parse-entities@npm:4.0.2"
  dependencies:
    "@types/unist": "npm:^2.0.0"
    character-entities-legacy: "npm:^3.0.0"
    character-reference-invalid: "npm:^2.0.0"
    decode-named-character-reference: "npm:^1.0.0"
    is-alphanumerical: "npm:^2.0.0"
    is-decimal: "npm:^2.0.0"
    is-hexadecimal: "npm:^2.0.0"
  checksum: 10/b0ce693d0b3d7ed1cea6fe814e6e077c71532695f01178e846269e9a2bc2f7ff34ca4bb8db80b48af0451100f25bb010df6591c9bb6306e4680ccb423d1e4038
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10/505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10/060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10/55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10/49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10/5e8845c159261adda6f09814d7725683257fcc85a18f329880ab4d7cc1d12830967eae5d5894e453f341710d5484b8fdbbd4d75181b4d6e1eb2f4dc7aeadc434
  languageName: node
  linkType: hard

"pg-cloudflare@npm:^1.2.5":
  version: 1.2.5
  resolution: "pg-cloudflare@npm:1.2.5"
  checksum: 10/13181a5d8243758bc6651426368097c89a2ff226d2ed8119f2777b15eea5e22953b5605b3d4861e68cd2109e1b08d3eea143e495bcefccaf7a0c8f70b69a0b51
  languageName: node
  linkType: hard

"pg-connection-string@npm:^2.8.5":
  version: 2.8.5
  resolution: "pg-connection-string@npm:2.8.5"
  checksum: 10/2a4e08ce9c86d67943870292745c939f981cb0105cc02f8459e2e2bbd265560060ac1cdf99126ef86371902ffe2624ae0171ce307e28ca4b7aec00725f1c80e1
  languageName: node
  linkType: hard

"pg-int8@npm:1.0.1":
  version: 1.0.1
  resolution: "pg-int8@npm:1.0.1"
  checksum: 10/a1e3a05a69005ddb73e5f324b6b4e689868a447c5fa280b44cd4d04e6916a344ac289e0b8d2695d66e8e89a7fba023affb9e0e94778770ada5df43f003d664c9
  languageName: node
  linkType: hard

"pg-numeric@npm:1.0.2":
  version: 1.0.2
  resolution: "pg-numeric@npm:1.0.2"
  checksum: 10/8899f8200caa1744439a8778a9eb3ceefb599d893e40a09eef84ee0d4c151319fd416634a6c0fc7b7db4ac268710042da5be700b80ef0de716fe089b8652c84f
  languageName: node
  linkType: hard

"pg-pool@npm:^3.9.6":
  version: 3.9.6
  resolution: "pg-pool@npm:3.9.6"
  peerDependencies:
    pg: ">=8.0"
  checksum: 10/245c04fa8e29fb2737cfe8ca6d8526b4b996932801aa9f5c2d95fc67a646a8ab770a5368fb2ed3513fc615c3e2843a5ab7670d2f8bb4dd33d8d122c0f98e64ed
  languageName: node
  linkType: hard

"pg-protocol@npm:*, pg-protocol@npm:^1.9.5":
  version: 1.9.5
  resolution: "pg-protocol@npm:1.9.5"
  checksum: 10/690b18d421fda1a004dcc07fe5aa06d3511775aae6ea424586e039c7954c463bc358bb00a93d064ccee16d378a7a9c27102fef348eee30f3d5d4105ff3d4151c
  languageName: node
  linkType: hard

"pg-types@npm:^2.1.0":
  version: 2.2.0
  resolution: "pg-types@npm:2.2.0"
  dependencies:
    pg-int8: "npm:1.0.1"
    postgres-array: "npm:~2.0.0"
    postgres-bytea: "npm:~1.0.0"
    postgres-date: "npm:~1.0.4"
    postgres-interval: "npm:^1.1.0"
  checksum: 10/87a84d4baa91378d3a3da6076c69685eb905d1087bf73525ae1ba84b291b9dd8738c6716b333d8eac6cec91bf087237adc3e9281727365e9cbab0d9d072778b1
  languageName: node
  linkType: hard

"pg-types@npm:^4.0.1":
  version: 4.0.2
  resolution: "pg-types@npm:4.0.2"
  dependencies:
    pg-int8: "npm:1.0.1"
    pg-numeric: "npm:1.0.2"
    postgres-array: "npm:~3.0.1"
    postgres-bytea: "npm:~3.0.0"
    postgres-date: "npm:~2.1.0"
    postgres-interval: "npm:^3.0.0"
    postgres-range: "npm:^1.1.1"
  checksum: 10/f4d529da864d4169afab300eb8629a84a6a06aa70c471160a7e46c34b6d4dd0e61cbd57d10d98c3a36e98f474e2ff85d41e4b1c953a321146b4bae09372c58d3
  languageName: node
  linkType: hard

"pg@npm:^8.11.3":
  version: 8.15.6
  resolution: "pg@npm:8.15.6"
  dependencies:
    pg-cloudflare: "npm:^1.2.5"
    pg-connection-string: "npm:^2.8.5"
    pg-pool: "npm:^3.9.6"
    pg-protocol: "npm:^1.9.5"
    pg-types: "npm:^2.1.0"
    pgpass: "npm:1.x"
  peerDependencies:
    pg-native: ">=3.0.1"
  dependenciesMeta:
    pg-cloudflare:
      optional: true
  peerDependenciesMeta:
    pg-native:
      optional: true
  checksum: 10/f465bc7cfbaee64f97e3a51be6a9c883d6ff29b0a825d4f8c3e0b24d1530a51c80f0a736d189c8db051cf18e9e60f20ddde4227adea8ca55700ab154c1abe21d
  languageName: node
  linkType: hard

"pgpass@npm:1.x":
  version: 1.0.5
  resolution: "pgpass@npm:1.0.5"
  dependencies:
    split2: "npm:^4.1.0"
  checksum: 10/0a6f3bf76e36bdb3c20a7e8033140c732767bba7e81f845f7489fc3123a2bd6e3b8e704f08cba86b117435414b5d2422e20ba9d5f2efb6f0c75c9efca73e8e87
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10/e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10/60c2595003b05e4535394d1da94850f5372c9427ca4413b71210f437f7b2ca091dbd611c45e8b37d10036fa8eade25c1b8951654f9d3973bfa66a2ff4d3b08bc
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10/ce617b8da36797d09c0baacb96ca8a44460452c89362d7cb8f70ca46b4158ba8bc3606912de7c818eb4a939f7f9015cef3c766ec8a0c6bfc725fdc078e39c717
  languageName: node
  linkType: hard

"pify@npm:^2.3.0":
  version: 2.3.0
  resolution: "pify@npm:2.3.0"
  checksum: 10/9503aaeaf4577acc58642ad1d25c45c6d90288596238fb68f82811c08104c800e5a7870398e9f015d82b44ecbcbef3dc3d4251a1cbb582f6e5959fe09884b2ba
  languageName: node
  linkType: hard

"pirates@npm:^4.0.1":
  version: 4.0.7
  resolution: "pirates@npm:4.0.7"
  checksum: 10/2427f371366081ae42feb58214f04805d6b41d6b84d74480ebcc9e0ddbd7105a139f7c653daeaf83ad8a1a77214cf07f64178e76de048128fec501eab3305a96
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.1.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: "npm:^4.0.0"
  checksum: 10/9863e3f35132bf99ae1636d31ff1e1e3501251d480336edb1c211133c8d58906bed80f154a1d723652df1fda91e01c7442c2eeaf9dc83157c7ae89087e43c8d6
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: 10/2f44137b8d3dd35f4a7ba7469eec1cd9cfbb46ec164b93a5bc1f4c3d68599c9910ee3b91da1d28b4560e9cc8414c3cd56fedc07259c67e52cc774476270d3302
  languageName: node
  linkType: hard

"postcss-import@npm:^15.1.0":
  version: 15.1.0
  resolution: "postcss-import@npm:15.1.0"
  dependencies:
    postcss-value-parser: "npm:^4.0.0"
    read-cache: "npm:^1.0.0"
    resolve: "npm:^1.1.7"
  peerDependencies:
    postcss: ^8.0.0
  checksum: 10/33c91b7e6b794b5c33d7d7d4730e5f0729c131d2de1ada7fcc116955625a78c3ce613983f019fa9447681795cf3f851e9c38dfbe3f48a2d08a8aef917c70a32a
  languageName: node
  linkType: hard

"postcss-js@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-js@npm:4.0.1"
  dependencies:
    camelcase-css: "npm:^2.0.1"
  peerDependencies:
    postcss: ^8.4.21
  checksum: 10/ef2cfe8554daab4166cfcb290f376e7387964c36503f5bd42008778dba735685af8d4f5e0aba67cae999f47c855df40a1cd31ae840e0df320ded36352581045e
  languageName: node
  linkType: hard

"postcss-load-config@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-load-config@npm:4.0.2"
  dependencies:
    lilconfig: "npm:^3.0.0"
    yaml: "npm:^2.3.4"
  peerDependencies:
    postcss: ">=8.0.9"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    postcss:
      optional: true
    ts-node:
      optional: true
  checksum: 10/e2c2ed9b7998a5b123e1ce0c124daf6504b1454c67dcc1c8fdbcc5ffb2597b7de245e3ac34f63afc928d3fd3260b1e36492ebbdb01a9ff63f16b3c8b7b925d1b
  languageName: node
  linkType: hard

"postcss-nested@npm:^6.2.0":
  version: 6.2.0
  resolution: "postcss-nested@npm:6.2.0"
  dependencies:
    postcss-selector-parser: "npm:^6.1.1"
  peerDependencies:
    postcss: ^8.2.14
  checksum: 10/d7f6ba6bfd03d42f84689a0630d4e393c421bb53723f16fe179a840f03ed17763b0fe494458577d2a015e857e0ec27c7e194909ffe209ee5f0676aec39737317
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.1.1, postcss-selector-parser@npm:^6.1.2":
  version: 6.1.2
  resolution: "postcss-selector-parser@npm:6.1.2"
  dependencies:
    cssesc: "npm:^3.0.0"
    util-deprecate: "npm:^1.0.2"
  checksum: 10/190034c94d809c115cd2f32ee6aade84e933450a43ec3899c3e78e7d7b33efd3a2a975bb45d7700b6c5b196c06a7d9acf3f1ba6f1d87032d9675a29d8bca1dd3
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.0, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 10/e4e4486f33b3163a606a6ed94f9c196ab49a37a7a7163abfcd469e5f113210120d70b8dd5e33d64636f41ad52316a3725655421eb9a1094f1bcab1db2f555c62
  languageName: node
  linkType: hard

"postcss@npm:8.4.31":
  version: 8.4.31
  resolution: "postcss@npm:8.4.31"
  dependencies:
    nanoid: "npm:^3.3.6"
    picocolors: "npm:^1.0.0"
    source-map-js: "npm:^1.0.2"
  checksum: 10/1a6653e72105907377f9d4f2cd341d8d90e3fde823a5ddea1e2237aaa56933ea07853f0f2758c28892a1d70c53bbaca200eb8b80f8ed55f13093003dbec5afa0
  languageName: node
  linkType: hard

"postcss@npm:^8.4.47, postcss@npm:^8.4.8":
  version: 8.5.3
  resolution: "postcss@npm:8.5.3"
  dependencies:
    nanoid: "npm:^3.3.8"
    picocolors: "npm:^1.1.1"
    source-map-js: "npm:^1.2.1"
  checksum: 10/6d7e21a772e8b05bf102636918654dac097bac013f0dc8346b72ac3604fc16829646f94ea862acccd8f82e910b00e2c11c1f0ea276543565d278c7ca35516a7c
  languageName: node
  linkType: hard

"postgres-array@npm:~2.0.0":
  version: 2.0.0
  resolution: "postgres-array@npm:2.0.0"
  checksum: 10/aff99e79714d1271fe942fec4ffa2007b755e7e7dc3d2feecae3f1ceecb86fd3637c8138037fc3d9e7ec369231eeb136843c0b25927bf1ce295245a40ef849b4
  languageName: node
  linkType: hard

"postgres-array@npm:~3.0.1":
  version: 3.0.4
  resolution: "postgres-array@npm:3.0.4"
  checksum: 10/9d0fed9f8a4674cbc31a4e568dc5d068f6e32b4b5c62deae2c4908c75303be0c5aef023fc04dfb8feaf6d63af1a17257e528ef606e8128bffe1f9d6844ad8ffa
  languageName: node
  linkType: hard

"postgres-bytea@npm:~1.0.0":
  version: 1.0.0
  resolution: "postgres-bytea@npm:1.0.0"
  checksum: 10/d844ae4ca7a941b70e45cac1261a73ee8ed39d72d3d74ab1d645248185a1b7f0ac91a3c63d6159441020f4e1f7fe64689ac56536a307b31cef361e5187335090
  languageName: node
  linkType: hard

"postgres-bytea@npm:~3.0.0":
  version: 3.0.0
  resolution: "postgres-bytea@npm:3.0.0"
  dependencies:
    obuf: "npm:~1.1.2"
  checksum: 10/f5c01758fd2fa807afbd34e1ba2146f683818ebc2d23f4a62f0fd627c0b1126fc543cab1b63925f97ce6c7d8f5f316043218619c447445210ea82f10411efb1b
  languageName: node
  linkType: hard

"postgres-date@npm:~1.0.4":
  version: 1.0.7
  resolution: "postgres-date@npm:1.0.7"
  checksum: 10/571ef45bec4551bb5d608c31b79987d7a895141f7d6c7b82e936a52d23d97474c770c6143e5cf8936c1cdc8b0dfd95e79f8136bf56a90164182a60f242c19f2b
  languageName: node
  linkType: hard

"postgres-date@npm:~2.1.0":
  version: 2.1.0
  resolution: "postgres-date@npm:2.1.0"
  checksum: 10/faa1c70dfad0e35bd4aa7cb6088fcd4e4f039aa25dc42150129178fc2a0baa7e37eca0bf18e4142a40dea18d1955459b08783f78ec487ef27b4b93ab5e854597
  languageName: node
  linkType: hard

"postgres-interval@npm:^1.1.0":
  version: 1.2.0
  resolution: "postgres-interval@npm:1.2.0"
  dependencies:
    xtend: "npm:^4.0.0"
  checksum: 10/746b71f93805ae33b03528e429dc624706d1f9b20ee81bf743263efb6a0cd79ae02a642a8a480dbc0f09547b4315ab7df6ce5ec0be77ed700bac42730f5c76b2
  languageName: node
  linkType: hard

"postgres-interval@npm:^3.0.0":
  version: 3.0.0
  resolution: "postgres-interval@npm:3.0.0"
  checksum: 10/c7a1cf006de97de663b6b8c4d2b167aa9909a238c4866a94b15d303762f5ac884ff4796cd6e2111b7f0a91302b83c570453aa8506fd005b5a5d5dfa87441bebc
  languageName: node
  linkType: hard

"postgres-range@npm:^1.1.1":
  version: 1.1.4
  resolution: "postgres-range@npm:1.1.4"
  checksum: 10/035759f17b44bf9ba7e71a30402ed2ca1e2b7fabb3ad794b08169a5b453d38d06905a6dfb51fe41a3f6d9fac4e183dac9e769b95053053db933be16785edce1f
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: 10/0b9d2c76801ca652a7f64892dd37b7e3fab149a37d2424920099bf894acccc62abb4424af2155ab36dea8744843060a2d8ddc983518d0b1e22265a22324b72ed
  languageName: node
  linkType: hard

"prettier@npm:^2.8.8":
  version: 2.8.8
  resolution: "prettier@npm:2.8.8"
  bin:
    prettier: bin-prettier.js
  checksum: 10/00cdb6ab0281f98306cd1847425c24cbaaa48a5ff03633945ab4c701901b8e96ad558eb0777364ffc312f437af9b5a07d0f45346266e8245beaf6247b9c62b24
  languageName: node
  linkType: hard

"prism-react-renderer@npm:^2.0.6":
  version: 2.4.1
  resolution: "prism-react-renderer@npm:2.4.1"
  dependencies:
    "@types/prismjs": "npm:^1.26.0"
    clsx: "npm:^2.0.0"
  peerDependencies:
    react: ">=16.0.0"
  checksum: 10/f76ea89b8b18c477eb74e9ddda2571b5c4d21142731f6733160723aa03567b17df315d7db68ffb1122c199750ece65578ecacb488559229b26db5474d6aae55b
  languageName: node
  linkType: hard

"prismjs@npm:^1.29.0":
  version: 1.30.0
  resolution: "prismjs@npm:1.30.0"
  checksum: 10/6b48a2439a82e5c6882f48ebc1564c3890e16463ba17ac10c3ad4f62d98dea5b5c915b172b63b83023a70ad4f5d7be3e8a60304420db34a161fae69dd4e3e2da
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10/35610bdb0177d3ab5d35f8827a429fb1dc2518d9e639f2151ac9007f01a061c30e0c635a970c9b00c39102216160f6ec54b62377c92fac3b7bfc2ad4b98d195c
  languageName: node
  linkType: hard

"promise-polyfill@npm:^8.3.0":
  version: 8.3.0
  resolution: "promise-polyfill@npm:8.3.0"
  checksum: 10/f735f59e464174f720fec9c41c5029ec1014e62e05d61e39d8d2290a0bc4dd7c36a0782d3202f1775d09d0b33a47fef289db38c437534769b187da22e03bfa23
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10/96e1a82453c6c96eef53a37a1d6134c9f2482f94068f98a59145d0986ca4e497bf110a410adf73857e588165eab3899f0ebcf7b3890c1b3ce802abc0d65967d4
  languageName: node
  linkType: hard

"prop-types@npm:^15.7.2, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: "npm:^1.4.0"
    object-assign: "npm:^4.1.1"
    react-is: "npm:^16.13.1"
  checksum: 10/7d959caec002bc964c86cdc461ec93108b27337dabe6192fb97d69e16a0c799a03462713868b40749bfc1caf5f57ef80ac3e4ffad3effa636ee667582a75e2c0
  languageName: node
  linkType: hard

"property-information@npm:^7.0.0":
  version: 7.0.0
  resolution: "property-information@npm:7.0.0"
  checksum: 10/55f443088456cddc2fe499d6f5895e68cbd465e39dc318ecc63a0d2432d1b918f51fb6d13f8b1adf8a78337bc4e608baa6e46afbe0c6d50d2e38588b2c409f86
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10/febdc4362bead22f9e2608ff0171713230b57aff9dddc1c273aa2a651fbd366f94b7d6a71d78342a7c0819906750351ca7f2edd26ea41b626d87d6a13d1bd059
  languageName: node
  linkType: hard

"qs@npm:^6.12.1":
  version: 6.14.0
  resolution: "qs@npm:6.14.0"
  dependencies:
    side-channel: "npm:^1.1.0"
  checksum: 10/a60e49bbd51c935a8a4759e7505677b122e23bf392d6535b8fc31c1e447acba2c901235ecb192764013cd2781723dc1f61978b5fdd93cc31d7043d31cdc01974
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10/72900df0616e473e824202113c3df6abae59150dfb73ed13273503127235320e9c8ca4aaaaccfd58cf417c6ca92a6e68ee9a5c3182886ae949a768639b388a7b
  languageName: node
  linkType: hard

"radix-ui@npm:1.1.2":
  version: 1.1.2
  resolution: "radix-ui@npm:1.1.2"
  dependencies:
    "@radix-ui/primitive": "npm:1.1.1"
    "@radix-ui/react-accessible-icon": "npm:1.1.1"
    "@radix-ui/react-accordion": "npm:1.2.2"
    "@radix-ui/react-alert-dialog": "npm:1.1.5"
    "@radix-ui/react-aspect-ratio": "npm:1.1.1"
    "@radix-ui/react-avatar": "npm:1.1.2"
    "@radix-ui/react-checkbox": "npm:1.1.3"
    "@radix-ui/react-collapsible": "npm:1.1.2"
    "@radix-ui/react-collection": "npm:1.1.1"
    "@radix-ui/react-compose-refs": "npm:1.1.1"
    "@radix-ui/react-context": "npm:1.1.1"
    "@radix-ui/react-context-menu": "npm:2.2.5"
    "@radix-ui/react-dialog": "npm:1.1.5"
    "@radix-ui/react-direction": "npm:1.1.0"
    "@radix-ui/react-dismissable-layer": "npm:1.1.4"
    "@radix-ui/react-dropdown-menu": "npm:2.1.5"
    "@radix-ui/react-focus-guards": "npm:1.1.1"
    "@radix-ui/react-focus-scope": "npm:1.1.1"
    "@radix-ui/react-form": "npm:0.1.1"
    "@radix-ui/react-hover-card": "npm:1.1.5"
    "@radix-ui/react-label": "npm:2.1.1"
    "@radix-ui/react-menu": "npm:2.1.5"
    "@radix-ui/react-menubar": "npm:1.1.5"
    "@radix-ui/react-navigation-menu": "npm:1.2.4"
    "@radix-ui/react-popover": "npm:1.1.5"
    "@radix-ui/react-popper": "npm:1.2.1"
    "@radix-ui/react-portal": "npm:1.1.3"
    "@radix-ui/react-presence": "npm:1.1.2"
    "@radix-ui/react-primitive": "npm:2.0.1"
    "@radix-ui/react-progress": "npm:1.1.1"
    "@radix-ui/react-radio-group": "npm:1.2.2"
    "@radix-ui/react-roving-focus": "npm:1.1.1"
    "@radix-ui/react-scroll-area": "npm:1.2.2"
    "@radix-ui/react-select": "npm:2.1.5"
    "@radix-ui/react-separator": "npm:1.1.1"
    "@radix-ui/react-slider": "npm:1.2.2"
    "@radix-ui/react-slot": "npm:1.1.1"
    "@radix-ui/react-switch": "npm:1.1.2"
    "@radix-ui/react-tabs": "npm:1.1.2"
    "@radix-ui/react-toast": "npm:1.2.5"
    "@radix-ui/react-toggle": "npm:1.1.1"
    "@radix-ui/react-toggle-group": "npm:1.1.1"
    "@radix-ui/react-toolbar": "npm:1.1.1"
    "@radix-ui/react-tooltip": "npm:1.1.7"
    "@radix-ui/react-use-callback-ref": "npm:1.1.0"
    "@radix-ui/react-use-controllable-state": "npm:1.1.0"
    "@radix-ui/react-use-escape-keydown": "npm:1.1.0"
    "@radix-ui/react-use-layout-effect": "npm:1.1.0"
    "@radix-ui/react-use-size": "npm:1.1.0"
    "@radix-ui/react-visually-hidden": "npm:1.1.1"
  peerDependencies:
    "@types/react": "*"
    "@types/react-dom": "*"
    react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
    "@types/react-dom":
      optional: true
  checksum: 10/cc73a610b39fa0f18db62be1187980c6df82ed5a3dbba2b2faea2e7c536044138d31d869cdf016b15b4cc0b44c8ad3e9e93999561b442ddba456d1374376ec71
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: "npm:^5.1.0"
  checksum: 10/4efd1ad3d88db77c2d16588dc54c2b52fd2461e70fe5724611f38d283857094fe09040fa2c9776366803c3152cf133171b452ef717592b65631ce5dc3a2bdafc
  languageName: node
  linkType: hard

"react-aria@npm:^3.33.1":
  version: 3.39.0
  resolution: "react-aria@npm:3.39.0"
  dependencies:
    "@internationalized/string": "npm:^3.2.6"
    "@react-aria/breadcrumbs": "npm:^3.5.23"
    "@react-aria/button": "npm:^3.13.0"
    "@react-aria/calendar": "npm:^3.8.0"
    "@react-aria/checkbox": "npm:^3.15.4"
    "@react-aria/color": "npm:^3.0.6"
    "@react-aria/combobox": "npm:^3.12.2"
    "@react-aria/datepicker": "npm:^3.14.2"
    "@react-aria/dialog": "npm:^3.5.24"
    "@react-aria/disclosure": "npm:^3.0.4"
    "@react-aria/dnd": "npm:^3.9.2"
    "@react-aria/focus": "npm:^3.20.2"
    "@react-aria/gridlist": "npm:^3.12.0"
    "@react-aria/i18n": "npm:^3.12.8"
    "@react-aria/interactions": "npm:^3.25.0"
    "@react-aria/label": "npm:^3.7.17"
    "@react-aria/landmark": "npm:^3.0.2"
    "@react-aria/link": "npm:^3.8.0"
    "@react-aria/listbox": "npm:^3.14.3"
    "@react-aria/menu": "npm:^3.18.2"
    "@react-aria/meter": "npm:^3.4.22"
    "@react-aria/numberfield": "npm:^3.11.13"
    "@react-aria/overlays": "npm:^3.27.0"
    "@react-aria/progress": "npm:^3.4.22"
    "@react-aria/radio": "npm:^3.11.2"
    "@react-aria/searchfield": "npm:^3.8.3"
    "@react-aria/select": "npm:^3.15.4"
    "@react-aria/selection": "npm:^3.24.0"
    "@react-aria/separator": "npm:^3.4.8"
    "@react-aria/slider": "npm:^3.7.18"
    "@react-aria/ssr": "npm:^3.9.8"
    "@react-aria/switch": "npm:^3.7.2"
    "@react-aria/table": "npm:^3.17.2"
    "@react-aria/tabs": "npm:^3.10.2"
    "@react-aria/tag": "npm:^3.5.2"
    "@react-aria/textfield": "npm:^3.17.2"
    "@react-aria/toast": "npm:^3.0.2"
    "@react-aria/tooltip": "npm:^3.8.2"
    "@react-aria/tree": "npm:^3.0.2"
    "@react-aria/utils": "npm:^3.28.2"
    "@react-aria/visually-hidden": "npm:^3.8.22"
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
    react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/76767a5c8bfe3f8fde479cedf7954dbd484f641b3805a300232df6295b579c3782f851768242f1fe31888c48475e7fc41fa042a234cfec75bc738a61d66044bd
  languageName: node
  linkType: hard

"react-country-flag@npm:^3.0.2":
  version: 3.1.0
  resolution: "react-country-flag@npm:3.1.0"
  peerDependencies:
    react: ">=16"
  checksum: 10/abe587bde4016ea313aa0fbc70d030beaedc86648e55c2f32bf49bd1f279ea2ed24adb64b6451c36ce0f16d609cf469d7e237cf86fe11dc25dc9dced3183a747
  languageName: node
  linkType: hard

"react-currency-input-field@npm:^3.6.11":
  version: 3.10.0
  resolution: "react-currency-input-field@npm:3.10.0"
  peerDependencies:
    react: ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10/764887b66af6d5f4e8c641a7e6c34ed2784bf39dcd83456ddac6e15756b6b138a27fceff6d90bcdd5f62540520e50bc33dc414dbf18a5a260e8a842c9e553eea
  languageName: node
  linkType: hard

"react-dom@npm:^19.1.0":
  version: 19.1.0
  resolution: "react-dom@npm:19.1.0"
  dependencies:
    scheduler: "npm:^0.26.0"
  peerDependencies:
    react: ^19.1.0
  checksum: 10/c5b58605862c7b0bb044416b01c73647bb8e89717fb5d7a2c279b11815fb7b49b619fe685c404e59f55eb52c66831236cc565c25ee1c2d042739f4a2cc538aa2
  languageName: node
  linkType: hard

"react-hook-form@npm:^7.53.0":
  version: 7.56.2
  resolution: "react-hook-form@npm:7.56.2"
  peerDependencies:
    react: ^16.8.0 || ^17 || ^18 || ^19
  checksum: 10/4cf58b99d5f5636aa5c679f0a05cdd9b3436b81dc89d9e5e0c17f2f64ea2b5037d8206804dbe971ed0b9a1bd05c289e0366c07a112e80fe20c1599d8a0e86f30
  languageName: node
  linkType: hard

"react-intersection-observer@npm:^9.3.4":
  version: 9.16.0
  resolution: "react-intersection-observer@npm:9.16.0"
  peerDependencies:
    react: ^17.0.0 || ^18.0.0 || ^19.0.0
    react-dom: ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    react-dom:
      optional: true
  checksum: 10/ded14524d9311cfb9dd9e65eb04748d07a1868f8c40dd628bec8a8474d43ee2373604fdc1e6a7d468a8e2e680638e41b91048ab9669555d50217c5c0c51247e0
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: 10/5aa564a1cde7d391ac980bedee21202fc90bdea3b399952117f54fb71a932af1e5902020144fb354b4690b2414a0c7aafe798eb617b76a3d441d956db7726fdf
  languageName: node
  linkType: hard

"react-markdown@npm:^9.0.1":
  version: 9.1.0
  resolution: "react-markdown@npm:9.1.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    devlop: "npm:^1.0.0"
    hast-util-to-jsx-runtime: "npm:^2.0.0"
    html-url-attributes: "npm:^3.0.0"
    mdast-util-to-hast: "npm:^13.0.0"
    remark-parse: "npm:^11.0.0"
    remark-rehype: "npm:^11.0.0"
    unified: "npm:^11.0.0"
    unist-util-visit: "npm:^5.0.0"
    vfile: "npm:^6.0.0"
  peerDependencies:
    "@types/react": ">=18"
    react: ">=18"
  checksum: 10/07045797b926afafc6aff692c9ee7d1cb9b12bc0e2d2b9f22ab17f2d1036dd807034a58565d67cfcfe94fe43961452a977496a175cbc9028ed51f2eec823c2f4
  languageName: node
  linkType: hard

"react-remove-scroll-bar@npm:^2.3.7":
  version: 2.3.8
  resolution: "react-remove-scroll-bar@npm:2.3.8"
  dependencies:
    react-style-singleton: "npm:^2.2.2"
    tslib: "npm:^2.0.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/6c0f8cff98b9f49a4ee2263f1eedf12926dced5ce220fbe83bd93544460e2a7ec8ec39b35d1b2a75d2fced0b2d64afeb8e66f830431ca896e05a20585f9fc350
  languageName: node
  linkType: hard

"react-remove-scroll@npm:^2.6.2, react-remove-scroll@npm:^2.6.3":
  version: 2.6.3
  resolution: "react-remove-scroll@npm:2.6.3"
  dependencies:
    react-remove-scroll-bar: "npm:^2.3.7"
    react-style-singleton: "npm:^2.2.3"
    tslib: "npm:^2.1.0"
    use-callback-ref: "npm:^1.3.3"
    use-sidecar: "npm:^1.1.3"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/d4dfd38e4381fa6059c8b810568b2d3a31fe21168bb3e2f57d1b1885ee08736fbd5a3fd83936faef0d17031c9c4175a1af83885bfc6c4280611f025447b19a4c
  languageName: node
  linkType: hard

"react-stately@npm:^3.31.1":
  version: 3.37.0
  resolution: "react-stately@npm:3.37.0"
  dependencies:
    "@react-stately/calendar": "npm:^3.8.0"
    "@react-stately/checkbox": "npm:^3.6.13"
    "@react-stately/collections": "npm:^3.12.3"
    "@react-stately/color": "npm:^3.8.4"
    "@react-stately/combobox": "npm:^3.10.4"
    "@react-stately/data": "npm:^3.12.3"
    "@react-stately/datepicker": "npm:^3.14.0"
    "@react-stately/disclosure": "npm:^3.0.3"
    "@react-stately/dnd": "npm:^3.5.3"
    "@react-stately/form": "npm:^3.1.3"
    "@react-stately/list": "npm:^3.12.1"
    "@react-stately/menu": "npm:^3.9.3"
    "@react-stately/numberfield": "npm:^3.9.11"
    "@react-stately/overlays": "npm:^3.6.15"
    "@react-stately/radio": "npm:^3.10.12"
    "@react-stately/searchfield": "npm:^3.5.11"
    "@react-stately/select": "npm:^3.6.12"
    "@react-stately/selection": "npm:^3.20.1"
    "@react-stately/slider": "npm:^3.6.3"
    "@react-stately/table": "npm:^3.14.1"
    "@react-stately/tabs": "npm:^3.8.1"
    "@react-stately/toast": "npm:^3.1.0"
    "@react-stately/toggle": "npm:^3.8.3"
    "@react-stately/tooltip": "npm:^3.5.3"
    "@react-stately/tree": "npm:^3.8.9"
    "@react-types/shared": "npm:^3.29.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
  checksum: 10/d79dabf63cbd33944a2c5184de6a64793feb76b27f40eb21236ee50e19aa8fa20cf48c522db2f9e1f80c9ed64ecf9ca08d857fac9377692b0ec4b72ff709e870
  languageName: node
  linkType: hard

"react-style-singleton@npm:^2.2.2, react-style-singleton@npm:^2.2.3":
  version: 2.2.3
  resolution: "react-style-singleton@npm:2.2.3"
  dependencies:
    get-nonce: "npm:^1.0.0"
    tslib: "npm:^2.0.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/62498094ff3877a37f351b29e6cad9e38b2eb1ac3c0cb27ebf80aee96554f80b35e17bdb552bcd7ac8b7cb9904fea93ea5668f2057c73d38f90b5d46bb9b27ab
  languageName: node
  linkType: hard

"react@npm:^19.1.0":
  version: 19.1.0
  resolution: "react@npm:19.1.0"
  checksum: 10/d0180689826fd9de87e839c365f6f361c561daea397d61d724687cae88f432a307d1c0f53a0ee95ddbe3352c10dac41d7ff1ad85530fb24951b27a39e5398db4
  languageName: node
  linkType: hard

"read-cache@npm:^1.0.0":
  version: 1.0.0
  resolution: "read-cache@npm:1.0.0"
  dependencies:
    pify: "npm:^2.3.0"
  checksum: 10/83a39149d9dfa38f0c482ea0d77b34773c92fef07fe7599cdd914d255b14d0453e0229ef6379d8d27d6947f42d7581635296d0cfa7708f05a9bd8e789d398b31
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10/196b30ef6ccf9b6e18c4e1724b7334f72a093d011a99f3b5920470f0b3406a51770867b3e1ae9711f227ef7a7065982f6ee2ce316746b2cb42c88efe44297fe7
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.9"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.7"
    get-proto: "npm:^1.0.1"
    which-builtin-type: "npm:^1.2.1"
  checksum: 10/80a4e2be716f4fe46a89a08ccad0863b47e8ce0f49616cab2d65dab0fbd53c6fdba0f52935fd41d37a2e4e22355c272004f920d63070de849f66eea7aeb4a081
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.3":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: "npm:^1.0.8"
    define-properties: "npm:^1.2.1"
    es-errors: "npm:^1.3.0"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    set-function-name: "npm:^2.0.2"
  checksum: 10/8ab897ca445968e0b96f6237641510f3243e59c180ee2ee8d83889c52ff735dd1bf3657fcd36db053e35e1d823dd53f2565d0b8021ea282c9fe62401c6c3bd6d
  languageName: node
  linkType: hard

"regexpp@npm:^3.2.0":
  version: 3.2.0
  resolution: "regexpp@npm:3.2.0"
  checksum: 10/3310010895a906873262f4b494fc99bcef1e71ef6720a0532c5999ca586498cbd4a284c8e3c2423f9d1d37512fd08d6064b7564e0e59508cf938f76dd15ace84
  languageName: node
  linkType: hard

"remark-parse@npm:^11.0.0":
  version: 11.0.0
  resolution: "remark-parse@npm:11.0.0"
  dependencies:
    "@types/mdast": "npm:^4.0.0"
    mdast-util-from-markdown: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
    unified: "npm:^11.0.0"
  checksum: 10/59d584be56ebc7c05524989c4ed86eb8a7b6e361942b705ca13a37349f60740a6073aedf7783af46ce920d09dd156148942d5e33e8be3dbcd47f818cb4bc410c
  languageName: node
  linkType: hard

"remark-rehype@npm:^11.0.0":
  version: 11.1.2
  resolution: "remark-rehype@npm:11.1.2"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    mdast-util-to-hast: "npm:^13.0.0"
    unified: "npm:^11.0.0"
    vfile: "npm:^6.0.0"
  checksum: 10/b5374a0bf08398431c92740d0cd9b20aea9df44cee12326820ddcc1b7ee642706604006461ea9799554c347e7caf31e7432132a03b97c508e1f77d29c423bd86
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: 10/839a3a890102a658f4cb3e7b2aa13a1f80a3a976b512020c3d1efc418491c48a886b6e481ea56afc6c4cb5eef678f23b2a4e70575e7534eccadf5e30ed2e56eb
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: 10/91eb76ce83621eea7bbdd9b55121a5c1c4a39e54a9ce04a9ad4517f102f8b5131c2cf07622c738a6683991bf54f2ce178f5a42803ecbd527ddc5105f362cc9e3
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 10/0763150adf303040c304009231314d1e84c6e5ebfa2d82b7d94e96a6e82bacd1dcc0b58ae257315f3c8adb89a91d8d0f12928241cba2df1680fbe6f60bf99b0e
  languageName: node
  linkType: hard

"resolve@npm:^1.1.7, resolve@npm:^1.22.4, resolve@npm:^1.22.8":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10/0a398b44da5c05e6e421d70108822c327675febb880eebe905587628de401854c61d5df02866ff34fc4cb1173a51c9f0e84a94702738df3611a62e2acdc68181
  languageName: node
  linkType: hard

"resolve@npm:^2.0.0-next.5":
  version: 2.0.0-next.5
  resolution: "resolve@npm:2.0.0-next.5"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10/2d6fd28699f901744368e6f2032b4268b4c7b9185fd8beb64f68c93ac6b22e52ae13560ceefc96241a665b985edf9ffd393ae26d2946a7d3a07b7007b7d51e79
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.1.7#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.4#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.8#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10/d4d878bfe3702d215ea23e75e0e9caf99468e3db76f5ca100d27ebdc527366fee3877e54bce7d47cc72ca8952fc2782a070d238bfa79a550eeb0082384c3b81a
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^2.0.0-next.5#optional!builtin<compat/resolve>":
  version: 2.0.0-next.5
  resolution: "resolve@patch:resolve@npm%3A2.0.0-next.5#optional!builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.13.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10/05fa778de9d0347c8b889eb7a18f1f06bf0f801b0eb4610b4871a4b2f22e220900cf0ad525e94f990bb8d8921c07754ab2122c0c225ab4cdcea98f36e64fa4c2
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10/1f914879f97e7ee931ad05fe3afa629bd55270fc6cf1c1e589b6a99fab96d15daad0fa1a52a00c729ec0078045fe3e399bd4fd0c93bcc906957bdc17f89cb8e6
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 10/af47851b547e8a8dc89af144fceee17b80d5beaf5e6f57ed086432d79943434ff67ca526e92275be6f54b6189f6920a24eace75c2657eed32d02c400312b21ec
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: "npm:^7.1.3"
  bin:
    rimraf: bin.js
  checksum: 10/063ffaccaaaca2cfd0ef3beafb12d6a03dd7ff1260d752d62a6077b5dfff6ae81bea571f655bb6b589d366930ec1bdd285d40d560c0dae9b12f125e54eb743d5
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10/cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    get-intrinsic: "npm:^1.2.6"
    has-symbols: "npm:^1.1.0"
    isarray: "npm:^2.0.5"
  checksum: 10/fac4f40f20a3f7da024b54792fcc61059e814566dcbb04586bfefef4d3b942b2408933f25b7b3dd024affd3f2a6bbc916bef04807855e4f192413941369db864
  languageName: node
  linkType: hard

"safe-buffer@npm:^5.1.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10/32872cd0ff68a3ddade7a7617b8f4c2ae8764d8b7d884c651b74457967a9e0e886267d3ecc781220629c44a865167b61c375d2da6c720c840ecd73f45d5d9451
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    isarray: "npm:^2.0.5"
  checksum: 10/2bd4e53b6694f7134b9cf93631480e7fafc8637165f0ee91d5a4af5e7f33d37de9562d1af5021178dd4217d0230cde8d6530fa28cfa1ebff9a431bf8fff124b4
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.0.3, safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    is-regex: "npm:^1.2.1"
  checksum: 10/ebdb61f305bf4756a5b023ad86067df5a11b26898573afe9e52a548a63c3bd594825d9b0e2dde2eb3c94e57e0e04ac9929d4107c394f7b8e56a4613bed46c69a
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10/7eaf7a0cf37cc27b42fb3ef6a9b1df6e93a1c6d98c6c6702b02fe262d5fcbd89db63320793b99b21cb5348097d0a53de81bd5f4e8b86e20cc9412e3f1cfb4e83
  languageName: node
  linkType: hard

"scheduler@npm:^0.26.0":
  version: 0.26.0
  resolution: "scheduler@npm:0.26.0"
  checksum: 10/1ecf2e5d7de1a7a132796834afe14a2d589ba7e437615bd8c06f3e0786a3ac3434655e67aac8755d9b14e05754c177e49c064261de2673aaa3c926bc98caa002
  languageName: node
  linkType: hard

"schema-utils@npm:^2.6.5":
  version: 2.7.1
  resolution: "schema-utils@npm:2.7.1"
  dependencies:
    "@types/json-schema": "npm:^7.0.5"
    ajv: "npm:^6.12.4"
    ajv-keywords: "npm:^3.5.2"
  checksum: 10/86c3038798981dbc702d5f6a86d4e4a308a2ec6e8eb1bf7d1a3ea95cb3f1972491833b76ce1c86a068652417019126d5b68219c33a9ad069358dd10429d4096d
  languageName: node
  linkType: hard

"schema-utils@npm:^4.3.0, schema-utils@npm:^4.3.2":
  version: 4.3.2
  resolution: "schema-utils@npm:4.3.2"
  dependencies:
    "@types/json-schema": "npm:^7.0.9"
    ajv: "npm:^8.9.0"
    ajv-formats: "npm:^2.1.1"
    ajv-keywords: "npm:^5.1.0"
  checksum: 10/02c32c34aae762d48468f98465a96a167fede637772871c7c7d8923671ddb9f20b2cc6f6e8448ae6bef5363e3597493c655212c8b06a4ee73aa099d9452fbd8b
  languageName: node
  linkType: hard

"semver@npm:^6.0.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10/1ef3a85bd02a760c6ef76a45b8c1ce18226de40831e02a00bad78485390b98b6ccaa31046245fc63bba4a47a6a592b6c7eedc65cc47126e60489f9cc1ce3ed7e
  languageName: node
  linkType: hard

"semver@npm:^7.3.5":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10/7a24cffcaa13f53c09ce55e05efe25cd41328730b2308678624f8b9f5fc3093fc4d189f47950f0b811ff8f3c3039c24a2c36717ba7961615c682045bf03e1dda
  languageName: node
  linkType: hard

"semver@npm:^7.6.0, semver@npm:^7.7.1":
  version: 7.7.1
  resolution: "semver@npm:7.7.1"
  bin:
    semver: bin/semver.js
  checksum: 10/4cfa1eb91ef3751e20fc52e47a935a0118d56d6f15a837ab814da0c150778ba2ca4f1a4d9068b33070ea4273629e615066664c2cfcd7c272caf7a8a0f6518b2c
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.2":
  version: 6.0.2
  resolution: "serialize-javascript@npm:6.0.2"
  dependencies:
    randombytes: "npm:^2.1.0"
  checksum: 10/445a420a6fa2eaee4b70cbd884d538e259ab278200a2ededd73253ada17d5d48e91fb1f4cd224a236ab62ea7ba0a70c6af29fc93b4f3d3078bf7da1c031fde58
  languageName: node
  linkType: hard

"server-only@npm:^0.0.1":
  version: 0.0.1
  resolution: "server-only@npm:0.0.1"
  checksum: 10/c432348956641ea3f460af8dc3765f3a1bdbcf7a1e0205b0756d868e6e6fe8934cdee6bff68401a1dd49ba4a831c75916517a877446d54b334f7de36fa273e53
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
    get-intrinsic: "npm:^1.2.4"
    gopd: "npm:^1.0.1"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10/505d62b8e088468917ca4e3f8f39d0e29f9a563b97dbebf92f4bd2c3172ccfb3c5b8e4566d5fcd00784a00433900e7cb8fbc404e2dbd8c3818ba05bb9d4a8a6d
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: "npm:^1.1.4"
    es-errors: "npm:^1.3.0"
    functions-have-names: "npm:^1.2.3"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10/c7614154a53ebf8c0428a6c40a3b0b47dac30587c1a19703d1b75f003803f73cdfa6a93474a9ba678fa565ef5fbddc2fae79bca03b7d22ab5fd5163dbe571a74
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/b87f8187bca595ddc3c0721ece4635015fd9d7cb294e6dd2e394ce5186a71bbfa4dc8a35010958c65e43ad83cde09642660e61a952883c24fd6b45ead15f045c
  languageName: node
  linkType: hard

"sharp@npm:^0.34.1":
  version: 0.34.1
  resolution: "sharp@npm:0.34.1"
  dependencies:
    "@img/sharp-darwin-arm64": "npm:0.34.1"
    "@img/sharp-darwin-x64": "npm:0.34.1"
    "@img/sharp-libvips-darwin-arm64": "npm:1.1.0"
    "@img/sharp-libvips-darwin-x64": "npm:1.1.0"
    "@img/sharp-libvips-linux-arm": "npm:1.1.0"
    "@img/sharp-libvips-linux-arm64": "npm:1.1.0"
    "@img/sharp-libvips-linux-ppc64": "npm:1.1.0"
    "@img/sharp-libvips-linux-s390x": "npm:1.1.0"
    "@img/sharp-libvips-linux-x64": "npm:1.1.0"
    "@img/sharp-libvips-linuxmusl-arm64": "npm:1.1.0"
    "@img/sharp-libvips-linuxmusl-x64": "npm:1.1.0"
    "@img/sharp-linux-arm": "npm:0.34.1"
    "@img/sharp-linux-arm64": "npm:0.34.1"
    "@img/sharp-linux-s390x": "npm:0.34.1"
    "@img/sharp-linux-x64": "npm:0.34.1"
    "@img/sharp-linuxmusl-arm64": "npm:0.34.1"
    "@img/sharp-linuxmusl-x64": "npm:0.34.1"
    "@img/sharp-wasm32": "npm:0.34.1"
    "@img/sharp-win32-ia32": "npm:0.34.1"
    "@img/sharp-win32-x64": "npm:0.34.1"
    color: "npm:^4.2.3"
    detect-libc: "npm:^2.0.3"
    semver: "npm:^7.7.1"
  dependenciesMeta:
    "@img/sharp-darwin-arm64":
      optional: true
    "@img/sharp-darwin-x64":
      optional: true
    "@img/sharp-libvips-darwin-arm64":
      optional: true
    "@img/sharp-libvips-darwin-x64":
      optional: true
    "@img/sharp-libvips-linux-arm":
      optional: true
    "@img/sharp-libvips-linux-arm64":
      optional: true
    "@img/sharp-libvips-linux-ppc64":
      optional: true
    "@img/sharp-libvips-linux-s390x":
      optional: true
    "@img/sharp-libvips-linux-x64":
      optional: true
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
    "@img/sharp-linux-arm":
      optional: true
    "@img/sharp-linux-arm64":
      optional: true
    "@img/sharp-linux-s390x":
      optional: true
    "@img/sharp-linux-x64":
      optional: true
    "@img/sharp-linuxmusl-arm64":
      optional: true
    "@img/sharp-linuxmusl-x64":
      optional: true
    "@img/sharp-wasm32":
      optional: true
    "@img/sharp-win32-ia32":
      optional: true
    "@img/sharp-win32-x64":
      optional: true
  checksum: 10/aecb960c0780b56134bfef01b7aeaa4e6650320a8a1f491237b45e900fc670830ee5d0600f30e51878328109db82e376bb526931d07a2e9358510ef30ab5abe8
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10/6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10/1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10/603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10/5771861f77feefe44f6195ed077a9e4f389acc188f895f570d56445e251b861754b547ea9ef73ecee4e01fdada6568bfe9020d2ec2dfc5571e9fa1bbc4a10615
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10/a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10/7d53b9db292c6262f326b6ff3bc1611db84ece36c2c7dc0e937954c13c73185b0406c56589e2bb8d071d6fee468e14c39fb5d203ee39be66b7b8174f179afaba
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10/c9fa63bbbd7431066174a48ba2dd9986dfd930c3a8b59de9c29d7b6854ec1c12a80d15310869ea5166d413b99f041bfa3dd80a7947bcd44ea8e6eb3ffeabfa1f
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: "npm:^0.3.1"
  checksum: 10/c6dffff17aaa383dae7e5c056fbf10cf9855a9f79949f20ee225c04f06ddde56323600e0f3d6797e82d08d006e93761122527438ee9531620031c08c9e0d73cc
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10/927484aa0b1640fd9473cee3e0a0bcad6fce93fd7bbc18bac9ad0c33686f5d2e2c422fba24b5899c184524af01e11dd2bd051c2bf2b07e47aff8ca72cbfc60d2
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10/ee99e1dacab0985b52cbe5a75640be6e604135e9489ebdc3048635d186012fbaecc20fbbe04b177dee434c319ba20f09b3e7dfefb7d932466c0d707744eac05c
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.4
  resolution: "socks@npm:2.8.4"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10/ab3af97aeb162f32c80e176c717ccf16a11a6ebb4656a62b94c0f96495ea2a1f4a8206c04b54438558485d83d0c5f61920c07a1a5d3963892a589b40cc6107dd
  languageName: node
  linkType: hard

"sonner@npm:^1.5.0":
  version: 1.7.4
  resolution: "sonner@npm:1.7.4"
  peerDependencies:
    react: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    react-dom: ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  checksum: 10/454dfedb9e91276dda38455fced43d66d84875048d5bf5e223c4d95167ca6fefb4d868ecec1c3bf5c9e91896dc6407adbde92eabb37a97126af4fb0856f96e61
  languageName: node
  linkType: hard

"source-map-js@npm:^1.0.2, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 10/ff9d8c8bf096d534a5b7707e0382ef827b4dd360a577d3f34d2b9f48e12c9d230b5747974ee7c607f0df65113732711bb701fe9ece3c7edbd43cb2294d707df3
  languageName: node
  linkType: hard

"source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10/8317e12d84019b31e34b86d483dd41d6f832f389f7417faf8fc5c75a66a12d9686e47f589a0554a868b8482f037e23df9d040d29387eb16fa14cb85f091ba207
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10/59ef7462f1c29d502b3057e822cdbdae0b0e565302c4dd1a95e11e793d8d9d62006cdc10e0fd99163ca33ff2071360cf50ee13f90440806e7ed57d81cba2f7ff
  languageName: node
  linkType: hard

"space-separated-tokens@npm:^2.0.0":
  version: 2.0.2
  resolution: "space-separated-tokens@npm:2.0.2"
  checksum: 10/202e97d7ca1ba0758a0aa4fe226ff98142073bcceeff2da3aad037968878552c3bbce3b3231970025375bbba5aee00c5b8206eda408da837ab2dc9c0f26be990
  languageName: node
  linkType: hard

"split2@npm:^4.1.0":
  version: 4.2.0
  resolution: "split2@npm:4.2.0"
  checksum: 10/09bbefc11bcf03f044584c9764cd31a252d8e52cea29130950b26161287c11f519807c5e54bd9e5804c713b79c02cefe6a98f4688630993386be353e03f534ab
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10/e7587128c423f7e43cc625fe2f87e6affdf5ca51c1cc468e910d8aaca46bb44a7fbcfa552f787b1d3987f7043aeb4527d1b99559e6621e01b42b3f45e5a24cbb
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10/7024c1a6e39b3f18aa8f1c8290e884fe91b0f9ca5a6c6d410544daad54de0ba664db879afe16412e187c6c292fd60b937f047ee44292e5c2af2dcc6d8e1a9b48
  languageName: node
  linkType: hard

"stable-hash@npm:^0.0.5":
  version: 0.0.5
  resolution: "stable-hash@npm:0.0.5"
  checksum: 10/9222ea2c558e37c4a576cb4e406966b9e6aa05b93f5c4f09ef4aaabe3577439b9b8fbff407b16840b63e2ae83de74290c7b1c2da7360d571e480e46a4aec0a56
  languageName: node
  linkType: hard

"streamsearch@npm:^1.1.0":
  version: 1.1.0
  resolution: "streamsearch@npm:1.1.0"
  checksum: 10/612c2b2a7dbcc859f74597112f80a42cbe4d448d03da790d5b7b39673c1197dd3789e91cd67210353e58857395d32c1e955a9041c4e6d5bae723436b3ed9ed14
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10/e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string.prototype.includes@npm:^2.0.1":
  version: 2.0.1
  resolution: "string.prototype.includes@npm:2.0.1"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.3"
  checksum: 10/939a5447e4a99a86f29cc97fa24f358e5071f79e34746de4c7eb2cd736ed626ad24870a1e356f33915b3b352bb87f7e4d1cebc15d1e1aaae0923777e21b1b28b
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.12":
  version: 4.0.12
  resolution: "string.prototype.matchall@npm:4.0.12"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.3"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.6"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    get-intrinsic: "npm:^1.2.6"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    internal-slot: "npm:^1.1.0"
    regexp.prototype.flags: "npm:^1.5.3"
    set-function-name: "npm:^2.0.2"
    side-channel: "npm:^1.1.0"
  checksum: 10/e4ab34b9e7639211e6c5e9759adb063028c5c5c4fc32ad967838b2bd1e5ce83a66ae8ec755d24a79302849f090b59194571b2c33471e86e7821b21c0f56df316
  languageName: node
  linkType: hard

"string.prototype.repeat@npm:^1.0.0":
  version: 1.0.0
  resolution: "string.prototype.repeat@npm:1.0.0"
  dependencies:
    define-properties: "npm:^1.1.3"
    es-abstract: "npm:^1.17.5"
  checksum: 10/4b1bd91b75fa8fdf0541625184ebe80e445a465ce4253c19c3bccd633898005dadae0f74b85ae72662a53aafb8035bf48f8f5c0755aec09bc106a7f13959d05e
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-data-property: "npm:^1.1.4"
    define-properties: "npm:^1.2.1"
    es-abstract: "npm:^1.23.5"
    es-object-atoms: "npm:^1.0.0"
    has-property-descriptors: "npm:^1.0.2"
  checksum: 10/47bb63cd2470a64bc5e2da1e570d369c016ccaa85c918c3a8bb4ab5965120f35e66d1f85ea544496fac84b9207a6b722adf007e6c548acd0813e5f8a82f9712a
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.8, string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.2"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/140c73899b6747de9e499c7c2e7a83d549c47a26fa06045b69492be9cfb9e2a95187499a373983a08a115ecff8bc3bd7b0fb09b8ff72fb2172abe766849272ef
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: "npm:^1.0.7"
    define-properties: "npm:^1.2.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10/160167dfbd68e6f7cb9f51a16074eebfce1571656fc31d40c3738ca9e30e35496f2c046fe57b6ad49f65f238a152be8c86fd9a2dd58682b5eba39dad995b3674
  languageName: node
  linkType: hard

"stringify-entities@npm:^4.0.0":
  version: 4.0.4
  resolution: "stringify-entities@npm:4.0.4"
  dependencies:
    character-entities-html4: "npm:^2.0.0"
    character-entities-legacy: "npm:^3.0.0"
  checksum: 10/42bd2f37528795a7b4386bd39dc4699515fb0f0b8c418a6bb29ae205ce66eaff9e8801a2bee65b8049c918c9475a71c7e5911f6a88c19f1d84ebdcba3d881a2d
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10/ae3b5436d34fadeb6096367626ce987057713c566e1e7768818797e00ac5d62023d0f198c4e681eae9e20701721980b26a64a8f5b91238869592a9c6800719a2
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10/475f53e9c44375d6e72807284024ac5d668ee1d06010740dec0b9744f2ddf47de8d7151f80e5f6190fc8f384e802fdf9504b76a7e9020c9faee7103623338be2
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 10/8d50ff27b7ebe5ecc78f1fe1e00fcdff7af014e73cf724b46fb81ef889eeb1015fc5184b64e81a2efe002180f3ba431bdd77e300da5c6685d702780fbf0c8d5b
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.0, strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10/492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"style-to-js@npm:^1.0.0":
  version: 1.1.16
  resolution: "style-to-js@npm:1.1.16"
  dependencies:
    style-to-object: "npm:1.0.8"
  checksum: 10/a876cc49a29ac90c7723b4d6f002ac6c1ac5ccc6b5bc963d9c607cfc74b15927b704c9324df6f824f576c65689fe4b4ff79caabcd44a13d8a02641f721f1b316
  languageName: node
  linkType: hard

"style-to-object@npm:1.0.8":
  version: 1.0.8
  resolution: "style-to-object@npm:1.0.8"
  dependencies:
    inline-style-parser: "npm:0.2.4"
  checksum: 10/530b067325e3119bfaf75bdbe25cc86b02b559db00d881a74b98a2d5bb10ac953d1b455ed90c825963cf3b4bdaa1bda45f406d78d987391434b8d8ab3835df4e
  languageName: node
  linkType: hard

"styled-jsx@npm:5.1.6":
  version: 5.1.6
  resolution: "styled-jsx@npm:5.1.6"
  dependencies:
    client-only: "npm:0.0.1"
  peerDependencies:
    react: ">= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0"
  peerDependenciesMeta:
    "@babel/core":
      optional: true
    babel-plugin-macros:
      optional: true
  checksum: 10/ba01200e8227fe1441a719c2e7da96c8aa7ef61d14211d1500e1abce12efa118479bcb6e7e12beecb9e1db76432caad2f4e01bbc0c9be21c134b088a4ca5ffe0
  languageName: node
  linkType: hard

"sucrase@npm:^3.35.0":
  version: 3.35.0
  resolution: "sucrase@npm:3.35.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.2"
    commander: "npm:^4.0.0"
    glob: "npm:^10.3.10"
    lines-and-columns: "npm:^1.1.6"
    mz: "npm:^2.7.0"
    pirates: "npm:^4.0.1"
    ts-interface-checker: "npm:^0.1.9"
  bin:
    sucrase: bin/sucrase
    sucrase-node: bin/sucrase-node
  checksum: 10/bc601558a62826f1c32287d4fdfa4f2c09fe0fec4c4d39d0e257fd9116d7d6227a18309721d4185ec84c9dc1af0d5ec0e05a42a337fbb74fc293e068549aacbe
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10/c8bb7afd564e3b26b50ca6ee47572c217526a1389fe018d00345856d4a9b08ffbd61fadaf283a87368d94c3dcdb8f5ffe2650a5a65863e21ad2730ca0f05210a
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10/157b534df88e39c5518c5e78c35580c1eca848d7dbaf31bbe06cdfc048e22c7ff1a9d046ae17b25691128f631a51d9ec373c1b740c12ae4f0de6e292037e4282
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10/a9dc19ae2220c952bd2231d08ddeecb1b0328b61e72071ff4000c8384e145cc07c1c0bdb3b5a1cb06e186a7b2790f1dee793418b332f6ddf320de25d9125be7e
  languageName: node
  linkType: hard

"tabbable@npm:^6.0.0":
  version: 6.2.0
  resolution: "tabbable@npm:6.2.0"
  checksum: 10/980fa73476026e99dcacfc0d6e000d41d42c8e670faf4682496d30c625495e412c4369694f2a15cf1e5252d22de3c396f2b62edbe8d60b5dadc40d09e3f2dde3
  languageName: node
  linkType: hard

"tailwind-merge@npm:^2.2.1":
  version: 2.6.0
  resolution: "tailwind-merge@npm:2.6.0"
  checksum: 10/a84f49d6f2cfb18ba06dc51b446b69f6b42cedcb9c9fa4e7c5f931b56e739870e513339e02b348b43f44011fa4852f02f72658a3f3e066e5101b09fc28644210
  languageName: node
  linkType: hard

"tailwindcss-animate@npm:^1.0.6":
  version: 1.0.7
  resolution: "tailwindcss-animate@npm:1.0.7"
  peerDependencies:
    tailwindcss: "*"
  checksum: 10/ef176fbb0bf9dca84178b35b6a9615cd756358ea80be9c575456d12ecd7f3c431e9e571915c7df72959dc798a730959e9a4739d59eab55d8cc6db390870ff0d2
  languageName: node
  linkType: hard

"tailwindcss@npm:^3.4.1":
  version: 3.4.17
  resolution: "tailwindcss@npm:3.4.17"
  dependencies:
    "@alloc/quick-lru": "npm:^5.2.0"
    arg: "npm:^5.0.2"
    chokidar: "npm:^3.6.0"
    didyoumean: "npm:^1.2.2"
    dlv: "npm:^1.1.3"
    fast-glob: "npm:^3.3.2"
    glob-parent: "npm:^6.0.2"
    is-glob: "npm:^4.0.3"
    jiti: "npm:^1.21.6"
    lilconfig: "npm:^3.1.3"
    micromatch: "npm:^4.0.8"
    normalize-path: "npm:^3.0.0"
    object-hash: "npm:^3.0.0"
    picocolors: "npm:^1.1.1"
    postcss: "npm:^8.4.47"
    postcss-import: "npm:^15.1.0"
    postcss-js: "npm:^4.0.1"
    postcss-load-config: "npm:^4.0.2"
    postcss-nested: "npm:^6.2.0"
    postcss-selector-parser: "npm:^6.1.2"
    resolve: "npm:^1.22.8"
    sucrase: "npm:^3.35.0"
  bin:
    tailwind: lib/cli.js
    tailwindcss: lib/cli.js
  checksum: 10/b0e00533ae3800223b5b71af9cb1dd9bfea5ef5ffa01300f1ced99de9511487aa41e03106173e4168c56c8f6600ee21c98c1d75a5def23cddf9b39b4ad71210d
  languageName: node
  linkType: hard

"tapable@npm:^2.1.1, tapable@npm:^2.2.0":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 10/1769336dd21481ae6347611ca5fca47add0962fd8e80466515032125eca0084a4f0ede11e65341b9c0018ef4e1cf1ad820adbb0fba7cc99865c6005734000b0a
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10/12a2a4fc6dee23e07cc47f1aeb3a14a1afd3f16397e1350036a8f4cdfee8dcac7ef5978337a4e7b2ac2c27a9a6d46388fc2088ea7c80cb6878c814b1425f8ecf
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^5.3.11":
  version: 5.3.14
  resolution: "terser-webpack-plugin@npm:5.3.14"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jest-worker: "npm:^27.4.5"
    schema-utils: "npm:^4.3.0"
    serialize-javascript: "npm:^6.0.2"
    terser: "npm:^5.31.1"
  peerDependencies:
    webpack: ^5.1.0
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    esbuild:
      optional: true
    uglify-js:
      optional: true
  checksum: 10/5b7290f7edb179b83cefb8827c12371ddddc088cf251cf58a1c738d82628331ae6604273b61fe991d77411d4bb6b7178c3826aa47edf01b4ee21f973d6c8b8fb
  languageName: node
  linkType: hard

"terser@npm:^5.31.1":
  version: 5.39.0
  resolution: "terser@npm:5.39.0"
  dependencies:
    "@jridgewell/source-map": "npm:^0.3.3"
    acorn: "npm:^8.8.2"
    commander: "npm:^2.20.0"
    source-map-support: "npm:~0.5.20"
  bin:
    terser: bin/terser
  checksum: 10/d84aff642398329f7179bbeaca28cac76a86100e2372d98d39d9b86c48023b6b9f797d983d6e7c0610b3f957c53d01ada1befa25d625614cb2ccd20714f1e98b
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: 10/4383b5baaeffa9bb4cda2ac33a4aa2e6d1f8aaf811848bf73513a9b88fd76372dc461f6fd6d2e9cb5100f48b473be32c6f95bd983509b7d92bb4d92c10747452
  languageName: node
  linkType: hard

"thenify-all@npm:^1.0.0":
  version: 1.6.0
  resolution: "thenify-all@npm:1.6.0"
  dependencies:
    thenify: "npm:>= 3.1.0 < 4"
  checksum: 10/dba7cc8a23a154cdcb6acb7f51d61511c37a6b077ec5ab5da6e8b874272015937788402fd271fdfc5f187f8cb0948e38d0a42dcc89d554d731652ab458f5343e
  languageName: node
  linkType: hard

"thenify@npm:>= 3.1.0 < 4":
  version: 3.3.1
  resolution: "thenify@npm:3.3.1"
  dependencies:
    any-promise: "npm:^1.0.0"
  checksum: 10/486e1283a867440a904e36741ff1a177faa827cf94d69506f7e3ae4187b9afdf9ec368b3d8da225c192bfe2eb943f3f0080594156bf39f21b57cd1411e2e7f6d
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10/3d306d319718b7cc9d79fb3f29d8655237aa6a1f280860a217f93417039d0614891aee6fc47c5db315f4fcc6ac8d55eb8e23e2de73b2c51a431b42456d9e5764
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.13":
  version: 0.2.13
  resolution: "tinyglobby@npm:0.2.13"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10/b04557ee58ad2be5f2d2cbb4b441476436c92bb45ba2e1fc464d686b793392b305ed0bcb8b877429e9b5036bdd46770c161a08384c0720b6682b7cd6ac80e403
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10/10dda13571e1f5ad37546827e9b6d4252d2e0bc176c24a101252153ef435d83696e2557fe128c4678e4e78f5f01e83711c703eef9814eb12dab028580d45980a
  languageName: node
  linkType: hard

"toggle-selection@npm:^1.0.6":
  version: 1.0.6
  resolution: "toggle-selection@npm:1.0.6"
  checksum: 10/9a0ed0ecbaac72b4944888dacd79fe0a55eeea76120a4c7e46b3bb3d85b24f086e90560bb22f5a965654a25ab43d79ec47dfdb3f1850ba740b14c5a50abc7040
  languageName: node
  linkType: hard

"trim-lines@npm:^3.0.0":
  version: 3.0.1
  resolution: "trim-lines@npm:3.0.1"
  checksum: 10/7a1325e4ce8ff7e9e52007600e9c9862a166d0db1f1cf0c9357e359e410acab1278fcd91cc279dfa5123fc37b69f080de02f471e91dbbc61b155b9ca92597929
  languageName: node
  linkType: hard

"trough@npm:^2.0.0":
  version: 2.2.0
  resolution: "trough@npm:2.2.0"
  checksum: 10/999c1cb3db6ec63e1663f911146a90125065da37f66ba342b031d53edb22a62f56c1f934bbc61a55b2b29dd74207544cfd78875b414665c1ffadcd9a9a009eeb
  languageName: node
  linkType: hard

"ts-api-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "ts-api-utils@npm:2.1.0"
  peerDependencies:
    typescript: ">=4.8.4"
  checksum: 10/02e55b49d9617c6eebf8aadfa08d3ca03ca0cd2f0586ad34117fdfc7aa3cd25d95051843fde9df86665ad907f99baed179e7a117b11021417f379e4d2614eacd
  languageName: node
  linkType: hard

"ts-interface-checker@npm:^0.1.9":
  version: 0.1.13
  resolution: "ts-interface-checker@npm:0.1.13"
  checksum: 10/9f7346b9e25bade7a1050c001ec5a4f7023909c0e1644c5a96ae20703a131627f081479e6622a4ecee2177283d0069e651e507bedadd3904fc4010ab28ffce00
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^3.15.0":
  version: 3.15.0
  resolution: "tsconfig-paths@npm:3.15.0"
  dependencies:
    "@types/json5": "npm:^0.0.29"
    json5: "npm:^1.0.2"
    minimist: "npm:^1.2.6"
    strip-bom: "npm:^3.0.0"
  checksum: 10/2041beaedc6c271fc3bedd12e0da0cc553e65d030d4ff26044b771fac5752d0460944c0b5e680f670c2868c95c664a256cec960ae528888db6ded83524e33a14
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.1.0, tslib@npm:^2.4.0, tslib@npm:^2.8.0":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10/3e2e043d5c2316461cb54e5c7fe02c30ef6dccb3384717ca22ae5c6b5bc95232a6241df19c622d9c73b809bea33b187f6dbc73030963e29950c2141bc32a79f7
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: "npm:^1.2.1"
  checksum: 10/14687776479d048e3c1dbfe58a2409e00367810d6960c0f619b33793271ff2a27f81b52461f14a162f1f89a9b1d8da1b237fc7c99b0e1fdcec28ec63a86b1fec
  languageName: node
  linkType: hard

"type-fest@npm:^0.20.2":
  version: 0.20.2
  resolution: "type-fest@npm:0.20.2"
  checksum: 10/8907e16284b2d6cfa4f4817e93520121941baba36b39219ea36acfe64c86b9dbc10c9941af450bd60832c8f43464974d51c0957f9858bc66b952b66b6914cbb9
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: "npm:^1.0.3"
    es-errors: "npm:^1.3.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10/3fb91f0735fb413b2bbaaca9fabe7b8fc14a3fa5a5a7546bab8a57e755be0e3788d893195ad9c2b842620592de0e68d4c077d4c2c41f04ec25b8b5bb82fa9a80
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.14"
  checksum: 10/269dad101dda73e3110117a9b84db86f0b5c07dad3a9418116fd38d580cab7fc628a4fc167e29b6d7c39da2f53374b78e7cb578b3c5ec7a556689d985d193519
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.2.0"
    has-proto: "npm:^1.2.0"
    is-typed-array: "npm:^1.1.15"
    reflect.getprototypeof: "npm:^1.0.9"
  checksum: 10/c2869aa584cdae24ecfd282f20a0f556b13a49a9d5bca1713370bb3c89dff0ccbc5ceb45cb5b784c98f4579e5e3e2a07e438c3a5b8294583e2bd4abbd5104fb5
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: "npm:^1.0.7"
    for-each: "npm:^0.3.3"
    gopd: "npm:^1.0.1"
    is-typed-array: "npm:^1.1.13"
    possible-typed-array-names: "npm:^1.0.0"
    reflect.getprototypeof: "npm:^1.0.6"
  checksum: 10/d6b2f0e81161682d2726eb92b1dc2b0890890f9930f33f9bcf6fc7272895ce66bc368066d273e6677776de167608adc53fcf81f1be39a146d64b630edbf2081c
  languageName: node
  linkType: hard

"typescript@npm:^5.5.3":
  version: 5.8.3
  resolution: "typescript@npm:5.8.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10/65c40944c51b513b0172c6710ee62e951b70af6f75d5a5da745cb7fab132c09ae27ffdf7838996e3ed603bb015dadd099006658046941bd0ba30340cc563ae92
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A^5.5.3#optional!builtin<compat/typescript>":
  version: 5.8.3
  resolution: "typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>::version=5.8.3&hash=74658d"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10/98470634034ec37fd9ea61cc82dcf9a27950d0117a4646146b767d085a2ec14b137aae9642a83d1c62732d7fdcdac19bb6288b0bb468a72f7a06ae4e1d2c72c9
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: "npm:^1.0.3"
    has-bigints: "npm:^1.0.2"
    has-symbols: "npm:^1.1.0"
    which-boxed-primitive: "npm:^1.1.1"
  checksum: 10/fadb347020f66b2c8aeacf8b9a79826fa34cc5e5457af4eb0bbc4e79bd87fed0fa795949825df534320f7c13f199259516ad30abc55a6e7b91d8d996ca069e50
  languageName: node
  linkType: hard

"undici-types@npm:~6.21.0":
  version: 6.21.0
  resolution: "undici-types@npm:6.21.0"
  checksum: 10/ec8f41aa4359d50f9b59fa61fe3efce3477cc681908c8f84354d8567bb3701fafdddf36ef6bff307024d3feb42c837cf6f670314ba37fc8145e219560e473d14
  languageName: node
  linkType: hard

"unified@npm:^11.0.0":
  version: 11.0.5
  resolution: "unified@npm:11.0.5"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    bail: "npm:^2.0.0"
    devlop: "npm:^1.0.0"
    extend: "npm:^3.0.0"
    is-plain-obj: "npm:^4.0.0"
    trough: "npm:^2.0.0"
    vfile: "npm:^6.0.0"
  checksum: 10/d9e6e88900a075f391b6bbf06f34062d41fa6257798110d1647753cfc2c6a6e2c1d016434e8ee35706c50485f9fb9ae4707a6a4790bd8dc461ec7e7315ed908b
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10/6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10/beafdf3d6f44990e0a5ce560f8f881b4ee811be70b6ba0db25298c31c8cf525ed963572b48cd03be1c1349084f9e339be4241666d7cf1ebdad20598d3c652b27
  languageName: node
  linkType: hard

"unist-util-is@npm:^6.0.0":
  version: 6.0.0
  resolution: "unist-util-is@npm:6.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10/edd6a93fb2255addf4b9eeb304c1da63c62179aef793169dd64ab955cf2f6814885fe25f95f8105893e3562dead348af535718d7a84333826e0491c04bf42511
  languageName: node
  linkType: hard

"unist-util-position@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-position@npm:5.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10/89d4da00e74618d7562ac7ac288961df9bcd4ccca6df3b5a90650f018eceb6b95de6e771e88bdbef46cc9d96861d456abe57b7ad1108921e0feb67c6292aa29d
  languageName: node
  linkType: hard

"unist-util-stringify-position@npm:^4.0.0":
  version: 4.0.0
  resolution: "unist-util-stringify-position@npm:4.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10/d15c88aca7a31902d95d5b5355bbe09583cf6f6ff6e59e134ef76c76d3c30bc1021f2d7ea5b7897c6d0858ed5f3770c1b19de9c78274f50d72f95a0d05f1af71
  languageName: node
  linkType: hard

"unist-util-visit-parents@npm:^6.0.0":
  version: 6.0.1
  resolution: "unist-util-visit-parents@npm:6.0.1"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-is: "npm:^6.0.0"
  checksum: 10/645b3cbc5e923bc692b1eb1a9ca17bffc5aabc25e6090ff3f1489bff8effd1890b28f7a09dc853cb6a7fa0da8581bfebc9b670a68b53c4c086cb9610dfd37701
  languageName: node
  linkType: hard

"unist-util-visit@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-visit@npm:5.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-is: "npm:^6.0.0"
    unist-util-visit-parents: "npm:^6.0.0"
  checksum: 10/f2bbde23641e9ade7640358c06ddeec0f38342322eb8e7819d9ee380b0f859d25d084dde22bf63db0280b3b2f36575f15aa1d6c23acf276c91c2493cf799e3b0
  languageName: node
  linkType: hard

"unrs-resolver@npm:^1.6.2":
  version: 1.7.2
  resolution: "unrs-resolver@npm:1.7.2"
  dependencies:
    "@unrs/resolver-binding-darwin-arm64": "npm:1.7.2"
    "@unrs/resolver-binding-darwin-x64": "npm:1.7.2"
    "@unrs/resolver-binding-freebsd-x64": "npm:1.7.2"
    "@unrs/resolver-binding-linux-arm-gnueabihf": "npm:1.7.2"
    "@unrs/resolver-binding-linux-arm-musleabihf": "npm:1.7.2"
    "@unrs/resolver-binding-linux-arm64-gnu": "npm:1.7.2"
    "@unrs/resolver-binding-linux-arm64-musl": "npm:1.7.2"
    "@unrs/resolver-binding-linux-ppc64-gnu": "npm:1.7.2"
    "@unrs/resolver-binding-linux-riscv64-gnu": "npm:1.7.2"
    "@unrs/resolver-binding-linux-riscv64-musl": "npm:1.7.2"
    "@unrs/resolver-binding-linux-s390x-gnu": "npm:1.7.2"
    "@unrs/resolver-binding-linux-x64-gnu": "npm:1.7.2"
    "@unrs/resolver-binding-linux-x64-musl": "npm:1.7.2"
    "@unrs/resolver-binding-wasm32-wasi": "npm:1.7.2"
    "@unrs/resolver-binding-win32-arm64-msvc": "npm:1.7.2"
    "@unrs/resolver-binding-win32-ia32-msvc": "npm:1.7.2"
    "@unrs/resolver-binding-win32-x64-msvc": "npm:1.7.2"
    napi-postinstall: "npm:^0.2.2"
  dependenciesMeta:
    "@unrs/resolver-binding-darwin-arm64":
      optional: true
    "@unrs/resolver-binding-darwin-x64":
      optional: true
    "@unrs/resolver-binding-freebsd-x64":
      optional: true
    "@unrs/resolver-binding-linux-arm-gnueabihf":
      optional: true
    "@unrs/resolver-binding-linux-arm-musleabihf":
      optional: true
    "@unrs/resolver-binding-linux-arm64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-arm64-musl":
      optional: true
    "@unrs/resolver-binding-linux-ppc64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-riscv64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-riscv64-musl":
      optional: true
    "@unrs/resolver-binding-linux-s390x-gnu":
      optional: true
    "@unrs/resolver-binding-linux-x64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-x64-musl":
      optional: true
    "@unrs/resolver-binding-wasm32-wasi":
      optional: true
    "@unrs/resolver-binding-win32-arm64-msvc":
      optional: true
    "@unrs/resolver-binding-win32-ia32-msvc":
      optional: true
    "@unrs/resolver-binding-win32-x64-msvc":
      optional: true
  checksum: 10/58daa4c659dec7d4ae7b9731d541b9faf1b702dec3d612f260b546dfd78de81c87ad3cd0eb362e364f9e86ccb3a6a42d05dc07936c39e82af58f5536c9b7de1c
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10/87af2776054ffb9194cf95e0201547d041f72ee44ce54b144da110e65ea7ca01379367407ba21de5c9edd52c74d95395366790de67f3eb4cc4afa0fe4424e76f
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10/b271ca7e3d46b7160222e3afa3e531505161c9a4e097febae9664e4b59912f4cbe94861361a4175edac3a03fee99d91e44b6a58c17a634bc5a664b19fc76fbcb
  languageName: node
  linkType: hard

"use-callback-ref@npm:^1.3.3":
  version: 1.3.3
  resolution: "use-callback-ref@npm:1.3.3"
  dependencies:
    tslib: "npm:^2.0.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/adf06a7b6a27d3651c325ac9b66d2b82ccacaed7450b85b211d123e91d9a23cb5a587fcc6db5b4fd07ac7233e5abf024d30cf02ddc2ec46bca712151c0836151
  languageName: node
  linkType: hard

"use-sidecar@npm:^1.1.3":
  version: 1.1.3
  resolution: "use-sidecar@npm:1.1.3"
  dependencies:
    detect-node-es: "npm:^1.1.0"
    tslib: "npm:^2.0.0"
  peerDependencies:
    "@types/react": "*"
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: 10/2fec05eb851cdfc4a4657b1dfb434e686f346c3265ffc9db8a974bb58f8128bd4a708a3cc00e8f51655fccf81822ed4419ebed42f41610589e3aab0cf2492edb
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.4.0, use-sync-external-store@npm:^1.5.0":
  version: 1.5.0
  resolution: "use-sync-external-store@npm:1.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10/ddae7c4572511f7f641d6977bd0725340aa7dbeda8250418b54c1a57ec285083d96cf50d1a1acbd6cf729f7a87071b2302c6fbd29310432bf1b21a961a313279
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.2":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10/474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"v8-compile-cache@npm:^2.0.3":
  version: 2.4.0
  resolution: "v8-compile-cache@npm:2.4.0"
  checksum: 10/49e726d7b2825ef7bc92187ecd57c59525957badbddb18fa529b0458b9280c59a1607ad3da4abe7808e9f9a00ec99b0fc07e485ffb7358cd5c11b2ef68d2145f
  languageName: node
  linkType: hard

"vfile-message@npm:^4.0.0":
  version: 4.0.2
  resolution: "vfile-message@npm:4.0.2"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-stringify-position: "npm:^4.0.0"
  checksum: 10/1a5a72bf4945a7103750a3001bd979088ce42f6a01efa8590e68b2425e1afc61ddc5c76f2d3c4a7053b40332b24c09982b68743223e99281158fe727135719fc
  languageName: node
  linkType: hard

"vfile@npm:^6.0.0":
  version: 6.0.3
  resolution: "vfile@npm:6.0.3"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    vfile-message: "npm:^4.0.0"
  checksum: 10/a5a85293c9eb8787aa42e180edaef00c13199a493d6ed82fecf13ab29a68526850788e22434d77808ea6b17a74e03ff899b9b4711df5b9eee75afcddd7c2e1fb
  languageName: node
  linkType: hard

"watchpack@npm:^2.4.1":
  version: 2.4.2
  resolution: "watchpack@npm:2.4.2"
  dependencies:
    glob-to-regexp: "npm:^0.4.1"
    graceful-fs: "npm:^4.1.2"
  checksum: 10/6bd4c051d9af189a6c781c3158dcb3069f432a0c144159eeb0a44117412105c61b2b683a5c9eebc4324625e0e9b76536387d0ba354594fa6cbbdf1ef60bee4c3
  languageName: node
  linkType: hard

"webpack-sources@npm:^3.2.3":
  version: 3.2.3
  resolution: "webpack-sources@npm:3.2.3"
  checksum: 10/a661f41795d678b7526ae8a88cd1b3d8ce71a7d19b6503da8149b2e667fc7a12f9b899041c1665d39e38245ed3a59ab68de648ea31040c3829aa695a5a45211d
  languageName: node
  linkType: hard

"webpack@npm:^5":
  version: 5.99.8
  resolution: "webpack@npm:5.99.8"
  dependencies:
    "@types/eslint-scope": "npm:^3.7.7"
    "@types/estree": "npm:^1.0.6"
    "@types/json-schema": "npm:^7.0.15"
    "@webassemblyjs/ast": "npm:^1.14.1"
    "@webassemblyjs/wasm-edit": "npm:^1.14.1"
    "@webassemblyjs/wasm-parser": "npm:^1.14.1"
    acorn: "npm:^8.14.0"
    browserslist: "npm:^4.24.0"
    chrome-trace-event: "npm:^1.0.2"
    enhanced-resolve: "npm:^5.17.1"
    es-module-lexer: "npm:^1.2.1"
    eslint-scope: "npm:5.1.1"
    events: "npm:^3.2.0"
    glob-to-regexp: "npm:^0.4.1"
    graceful-fs: "npm:^4.2.11"
    json-parse-even-better-errors: "npm:^2.3.1"
    loader-runner: "npm:^4.2.0"
    mime-types: "npm:^2.1.27"
    neo-async: "npm:^2.6.2"
    schema-utils: "npm:^4.3.2"
    tapable: "npm:^2.1.1"
    terser-webpack-plugin: "npm:^5.3.11"
    watchpack: "npm:^2.4.1"
    webpack-sources: "npm:^3.2.3"
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: 10/e66b9bcc0ae2ea7fd08b90a551ecf066bf71841923d744edc83713a7fdacd0c121a1f6236164d1d18fce6d44642f2960cee2a102e5445c2ef7634c457334c9ae
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: "npm:^1.1.0"
    is-boolean-object: "npm:^1.2.1"
    is-number-object: "npm:^1.1.1"
    is-string: "npm:^1.1.1"
    is-symbol: "npm:^1.1.1"
  checksum: 10/a877c0667bc089518c83ad4d845cf8296b03efe3565c1de1940c646e00a2a1ae9ed8a185bcfa27cbf352de7906f0616d83b9d2f19ca500ee02a551fb5cf40740
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    function.prototype.name: "npm:^1.1.6"
    has-tostringtag: "npm:^1.0.2"
    is-async-function: "npm:^2.0.0"
    is-date-object: "npm:^1.1.0"
    is-finalizationregistry: "npm:^1.1.0"
    is-generator-function: "npm:^1.0.10"
    is-regex: "npm:^1.2.1"
    is-weakref: "npm:^1.0.2"
    isarray: "npm:^2.0.5"
    which-boxed-primitive: "npm:^1.1.0"
    which-collection: "npm:^1.0.2"
    which-typed-array: "npm:^1.1.16"
  checksum: 10/22c81c5cb7a896c5171742cd30c90d992ff13fb1ea7693e6cf80af077791613fb3f89aa9b4b7f890bd47b6ce09c6322c409932359580a2a2a54057f7b52d1cbe
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: "npm:^2.0.3"
    is-set: "npm:^2.0.3"
    is-weakmap: "npm:^2.0.2"
    is-weakset: "npm:^2.0.3"
  checksum: 10/674bf659b9bcfe4055f08634b48a8588e879161b9fefed57e9ec4ff5601e4d50a05ccd76cf10f698ef5873784e5df3223336d56c7ce88e13bcf52ebe582fc8d7
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.18":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: "npm:^1.0.7"
    call-bind: "npm:^1.0.8"
    call-bound: "npm:^1.0.4"
    for-each: "npm:^0.3.5"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-tostringtag: "npm:^1.0.2"
  checksum: 10/12be30fb88567f9863186bee1777f11bea09dd59ed8b3ce4afa7dd5cade75e2f4cc56191a2da165113cc7cf79987ba021dac1e22b5b62aa7e5c56949f2469a68
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10/4782f8a1d6b8fc12c65e968fea49f59752bf6302dc43036c3bf87da718a80710f61a062516e9764c70008b487929a73546125570acea95c5b5dcc8ac3052c70f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10/6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: 10/1ec6f6089f205f83037be10d0c4b34c9183b0b63fca0834a5b3cee55dd321429d73d40bb44c8fc8471b5203d6e8f8275717f49a8ff4b2b0ab41d7e1b563e0854
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10/cebdaeca3a6880da410f75209e68cd05428580de5ad24535f22696d7d9cab134d1f8498599f344c3cf0fb37c1715807a183778d8c648d6cc0cb5ff2bb4236540
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10/7b1e4b35e9bb2312d2ee9ee7dc95b8cb5f8b4b5a89f7dde5543fe66c1e3715663094defa50d75454ac900bd210f702d575f15f3f17fa9ec0291806d2578d1ddf
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10/159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: 10/ac5dfa738b21f6e7f0dd6e65e1b3155036d68104e67e5d5d1bde74892e327d7e5636a076f625599dc394330a731861e87343ff184b0047fef1360a7ec0a5a36a
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10/9af0a4329c3c6b779ac4736c69fae4190ac03029fa27c1aef4e6bcc92119b73dea6fe5db5fe881fb0ce2a0e9539a42cdf60c7c21eda04d1a0b8c082e38509efb
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10/4cb02b42b8a93b5cf50caf5d8e9beb409400a8a4d85e83bb0685c1457e9ac0b7a00819e9f5991ac25ffabb56a78e2f017c1acc010b3a1babfe6de690ba531abd
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10/1884d272d485845ad04759a255c71775db0fac56308764b4c77ea56a20d56679fad340213054c8c9c9c26fcfd4c4b2a90df993b7e0aaf3cdb73c618d1d1a802a
  languageName: node
  linkType: hard

"yaml@npm:^2.3.4":
  version: 2.7.1
  resolution: "yaml@npm:2.7.1"
  bin:
    yaml: bin.mjs
  checksum: 10/af57658d37c5efae4bac7204589b742ae01878a278554d632f01012868cf7fa66cba09b39140f12e7f6ceecc693ae52bcfb737596c4827e6e233338cb3a9528e
  languageName: node
  linkType: hard

"zod@npm:3.22.4":
  version: 3.22.4
  resolution: "zod@npm:3.22.4"
  checksum: 10/73622ca36a916f785cf528fe612a884b3e0f183dbe6b33365a7d0fc92abdbedf7804c5e2bd8df0a278e1472106d46674281397a3dd800fa9031dc3429758c6ac
  languageName: node
  linkType: hard

"zwitch@npm:^2.0.0":
  version: 2.0.4
  resolution: "zwitch@npm:2.0.4"
  checksum: 10/f22ec5fc2d5f02c423c93d35cdfa83573a3a3bd98c66b927c368ea4d0e7252a500df2a90a6b45522be536a96a73404393c958e945fdba95e6832c200791702b6
  languageName: node
  linkType: hard
