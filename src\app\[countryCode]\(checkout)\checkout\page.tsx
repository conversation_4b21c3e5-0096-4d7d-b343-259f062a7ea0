import { retrieveCart } from "@/lib/data/cart"
import { retrieveCustomer } from "@/lib/data/customer"
import Wrapper from "@/modules/checkout/components/payment-wrapper"
import CheckoutForm from "@/modules/checkout/templates/checkout-form"
import CheckoutSummary from "@/modules/checkout/templates/checkout-summary"
import { B2BCart } from "@/types/global"
import { Metadata } from "next"
import { notFound } from "next/navigation"

export const metadata: Metadata = {
  title: "Checkout",
}

export default async function Checkout({
  searchParams,
}: {
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  try {
    const resolvedSearchParams = await searchParams
    const cartId = (resolvedSearchParams?.cart_id || resolvedSearchParams?.cartId) as string

    if (!cartId) {
      console.error("No cart ID provided in search params")
      return notFound()
    }

    const cart = (await retrieveCart(cartId)) as B2BCart

    if (!cart) {
      console.error(`Cart not found for ID: ${cartId}`)
      return notFound()
    }

    const customer = await retrieveCustomer().catch((error) => {
      console.error("Error retrieving customer:", error)
      return null
    })

    return (
      <Wrapper cart={cart}>
        <div className="grid grid-cols-1 small:grid-cols-[1fr_416px] content-container gap-2 py-24 h-full">
          <CheckoutForm cart={cart} customer={customer} />
          <div className="relative">
            <CheckoutSummary cart={cart} />
          </div>
        </div>
      </Wrapper>
    )
  } catch (error) {
    console.error("Error in Checkout component:", error)
    return notFound()
  }
}
