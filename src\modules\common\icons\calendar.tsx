import { SVGProps } from "react"

const CalendarIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="13"
    height="12"
    viewBox="0 0 13 12"
    fill="none"
    {...props}
  >
    <g clipPath="url(#clip0_21144_3572)">
      <path
        d="M4.23145 8C4.78373 8 5.23145 7.55228 5.23145 7C5.23145 6.44772 4.78373 6 4.23145 6C3.67916 6 3.23145 6.44772 3.23145 7C3.23145 7.55228 3.67916 8 4.23145 8Z"
        fill="#18181B"
      />
      <path
        d="M9.23145 1.525V0.75C9.23145 0.336 8.89544 0 8.48145 0C8.06745 0 7.73145 0.336 7.73145 0.75V1.5H4.73145V0.75C4.73145 0.336 4.39545 0 3.98145 0C3.56745 0 3.23145 0.336 3.23145 0.75V1.525C1.83345 1.654 0.731445 2.819 0.731445 4.25V9.25C0.731445 10.767 1.96445 12 3.48145 12H8.98145C10.4984 12 11.7314 10.767 11.7314 9.25V4.25C11.7314 2.819 10.6294 1.654 9.23145 1.525ZM10.2314 9.25C10.2314 9.939 9.67045 10.5 8.98145 10.5H3.48145C2.79245 10.5 2.23145 9.939 2.23145 9.25V5H10.2314V9.25Z"
        fill="#18181B"
      />
    </g>
    <defs>
      <clipPath id="clip0_21144_3572">
        <rect
          width="12"
          height="12"
          fill="white"
          transform="translate(0.231445)"
        />
      </clipPath>
    </defs>
  </svg>
)

export default CalendarIcon
